const fs = require('fs');
const path = require('path');

// Load tokens from the tokens folder (same as example-usage.js)
function loadTokensFromFolder() {
  const tokensDir = path.join(__dirname, '../apollo-token/build/tokens');
  const tokens = { global: {}, base: {}, alias: {} };

  // Helper function to deep merge objects (same as example-usage.js)
  function deepMerge(target, source) {
    for (const key in source) {
      if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
        if (!target[key]) target[key] = {};
        deepMerge(target[key], source[key]);
      } else {
        target[key] = source[key];
      }
    }
    return target;
  }

  // Read global tokens
  const globalDir = path.join(tokensDir, 'global');
  if (fs.existsSync(globalDir)) {
    const globalFiles = fs.readdirSync(globalDir).filter(file => file.endsWith('.json'));
    console.log(`📁 Reading ${globalFiles.length} global token files`);
    globalFiles.forEach(file => {
      const filePath = path.join(globalDir, file);
      const fileContent = JSON.parse(fs.readFileSync(filePath, 'utf8'));
      deepMerge(tokens, fileContent);
    });
  }

  // Read base tokens from system folder
  const systemDir = path.join(tokensDir, 'system');
  const baseDir = path.join(systemDir, 'base');
  if (fs.existsSync(baseDir)) {
    const baseFiles = fs.readdirSync(baseDir).filter(file => file.endsWith('.json'));
    console.log(`📁 Reading ${baseFiles.length} base token files`);
    baseFiles.forEach(file => {
      const filePath = path.join(baseDir, file);
      const fileContent = JSON.parse(fs.readFileSync(filePath, 'utf8'));
      deepMerge(tokens, fileContent);
    });
  }

  // Read alias tokens from system folder
  const aliasDir = path.join(systemDir, 'alias');
  if (fs.existsSync(aliasDir)) {
    const aliasFiles = fs.readdirSync(aliasDir).filter(file => file.endsWith('.json'));
    console.log(`📁 Reading ${aliasFiles.length} alias token files`);
    aliasFiles.forEach(file => {
      const filePath = path.join(aliasDir, file);
      const fileContent = JSON.parse(fs.readFileSync(filePath, 'utf8'));
      deepMerge(tokens, fileContent);
    });
  }

  return tokens;
}

const tokens = loadTokensFromFolder();



// Token resolution using the same approach as example-usage.js
function resolveTokenReferences(tokens) {
  const resolved = {};

  // Flatten tokens into a flat structure (same as example-usage.js)
  function flattenTokens(obj, prefix = '') {
    const flattened = {};

    for (const [key, value] of Object.entries(obj)) {
      if (key.startsWith('$')) {
        continue; // Skip metadata properties
      }

      const currentPath = prefix ? `${prefix}.${key}` : key;

      if (value && typeof value === 'object' && value.$value !== undefined) {
        // This is a token
        flattened[currentPath] = value;
      } else if (value && typeof value === 'object') {
        // This is a group, recurse
        Object.assign(flattened, flattenTokens(value, currentPath));
      }
    }

    return flattened;
  }

  // Convert any token value to CSS (same as example-usage.js)
  function tokenToCSS(token) {
    const value = token.$value;

    // Handle token references
    if (typeof value === 'string' && value.startsWith('{') && value.endsWith('}')) {
      const refPath = value.slice(1, -1).replace(/\./g, '-').toLowerCase();
      return `var(--apl-${refPath})`;
    }

    switch (token.$type) {
      case 'color':
        if (typeof value === 'object' && value.hex) {
          return value.alpha !== undefined ?
            `${value.hex}${Math.round(value.alpha * 255).toString(16).padStart(2, '0')}` :
            value.hex;
        }
        if (typeof value === 'object' && value.components) {
          const [r, g, b] = value.components.map(c => Math.round(c * 255));
          return value.alpha !== undefined ?
            `rgba(${r}, ${g}, ${b}, ${value.alpha})` :
            `rgb(${r}, ${g}, ${b})`;
        }
        break;
      case 'dimension':
        if (typeof value === 'object' && value.value !== undefined && value.unit !== undefined) {
          return `${value.value}${value.unit}`;
        }
        break;
      case 'fontFamily':
        return Array.isArray(value) ?
          value.map(f => `"${f}"`).join(', ') :
          `"${value}"`;
      case 'fontWeight':
        return value.toString();
      case 'number':
        return value.toString();
      default:
        return value.toString();
    }

    return value.toString();
  }

  // Flatten all tokens
  const flattened = flattenTokens(tokens);
  console.log(`Found ${Object.keys(flattened).length} total tokens`);

  // Convert to CSS variables with apl prefix (same as example-usage.js approach)
  for (const [path, token] of Object.entries(flattened)) {
    const cssVar = `--apl-${path.replace(/\./g, '-').replace(/\//g, '-').toLowerCase()}`;
    const cssValue = tokenToCSS(token);
    resolved[cssVar] = cssValue;
  }

  return resolved;
}

// Test the resolution
console.log('Testing token resolution...');
console.log('Alias tokens loaded:', Object.keys(tokens.alias || {}));
const resolvedTokens = resolveTokenReferences(tokens);

// Check some specific examples
const testCases = [
  '--apl-base-spacing-space15',
  '--apl-alias-spacing-margin-vertical-vertical',
  '--apl-base-color-tertiary-40',
  '--apl-global-spacing-48'
];

console.log('\nTest cases:');
testCases.forEach(testCase => {
  const value = resolvedTokens[testCase];
  console.log(`${testCase}: ${value || 'NOT FOUND'}`);
});

// Debug: Show some alias spacing tokens
console.log('\nSample alias spacing tokens:');
Object.keys(resolvedTokens)
  .filter(key => key.includes('alias-spacing'))
  .slice(0, 5)
  .forEach(key => {
    console.log(`${key}: ${resolvedTokens[key]}`);
  });

// Check for any unresolved references
const unresolvedTokens = Object.entries(resolvedTokens)
  .filter(([name, value]) => typeof value === 'string' && value.includes('{'))
  .slice(0, 10); // Show first 10

if (unresolvedTokens.length > 0) {
  console.log('\nUnresolved token references found:');
  unresolvedTokens.forEach(([name, value]) => {
    console.log(`${name}: ${value}`);
  });
} else {
  console.log('\nAll token references resolved successfully!');
}

// Generate CSS file
const cssContent = Object.entries(resolvedTokens)
  .map(([name, value]) => `  ${name}: ${value};`)
  .join('\n');

const fullCss = `:root {\n${cssContent}\n}`;

// Write CSS file
const cssOutputPath = path.join(__dirname, 'resolved-tokens.css');
fs.writeFileSync(cssOutputPath, fullCss);

console.log(`\nGenerated CSS file: ${cssOutputPath}`);
console.log(`Total resolved tokens: ${Object.keys(resolvedTokens).length}`);

// Show some sample output
console.log('\nSample resolved tokens:');
Object.entries(resolvedTokens).slice(0, 10).forEach(([name, value]) => {
  console.log(`${name}: ${value}`);
});
