:root {
  --apl-global-color-green-pine-0: #000000;
  --apl-global-color-green-pine-10: #002109;
  --apl-global-color-green-pine-20: #003915;
  --apl-global-color-green-pine-30: #005321;
  --apl-global-color-green-pine-40: #016E2E;
  --apl-global-color-green-pine-50: #2C8745;
  --apl-global-color-green-pine-60: #49A25C;
  --apl-global-color-green-pine-70: #64BE74;
  --apl-global-color-green-pine-80: #7FDA8E;
  --apl-global-color-green-pine-90: #9BF7A7;
  --apl-global-color-green-pine-95: #C5FFC8;
  --apl-global-color-green-pine-99: #F6FFF2;
  --apl-global-color-green-pine-100: #FFFFFF;
  --apl-global-color-gray-smoke-0: #000000;
  --apl-global-color-gray-smoke-10: #1C1B1C;
  --apl-global-color-gray-smoke-20: #313031;
  --apl-global-color-gray-smoke-30: #474647;
  --apl-global-color-gray-smoke-40: #5F5E5F;
  --apl-global-color-gray-smoke-50: #787777;
  --apl-global-color-gray-smoke-60: #929091;
  --apl-global-color-gray-smoke-70: #ADABAB;
  --apl-global-color-gray-smoke-80: #C8C6C6;
  --apl-global-color-gray-smoke-90: #E5E2E2;
  --apl-global-color-gray-smoke-95: #F3F0F0;
  --apl-global-color-gray-smoke-99: #F8F7F7;
  --apl-global-color-gray-smoke-100: #FFFFFF;
  --apl-global-color-blue-ocean-0: #000000;
  --apl-global-color-blue-ocean-10: #001159;
  --apl-global-color-blue-ocean-20: #00218C;
  --apl-global-color-blue-ocean-30: #0032C4;
  --apl-global-color-blue-ocean-40: #2E4EDC;
  --apl-global-color-blue-ocean-50: #4C6AF6;
  --apl-global-color-blue-ocean-60: #7087FF;
  --apl-global-color-blue-ocean-70: #95A6FF;
  --apl-global-color-blue-ocean-80: #B9C3FF;
  --apl-global-color-blue-ocean-90: #DEE1FF;
  --apl-global-color-blue-ocean-95: #F0EFFF;
  --apl-global-color-blue-ocean-99: #FEFBFF;
  --apl-global-color-blue-ocean-100: #FFFFFF;
  --apl-global-color-gray-bluish-0: #000000;
  --apl-global-color-gray-bluish-10: #131C2B;
  --apl-global-color-gray-bluish-20: #283141;
  --apl-global-color-gray-bluish-30: #3E4758;
  --apl-global-color-gray-bluish-40: #565F71;
  --apl-global-color-gray-bluish-50: #6E778A;
  --apl-global-color-gray-bluish-60: #8891A4;
  --apl-global-color-gray-bluish-70: #A2ABC0;
  --apl-global-color-gray-bluish-80: #BEC7DB;
  --apl-global-color-gray-bluish-90: #DAE3F8;
  --apl-global-color-gray-bluish-95: #ECF0FF;
  --apl-global-color-gray-bluish-99: #FDFBFF;
  --apl-global-color-gray-bluish-100: #FFFFFF;
  --apl-global-color-red-cherry-0: #000000;
  --apl-global-color-red-cherry-10: #410001;
  --apl-global-color-red-cherry-20: #690003;
  --apl-global-color-red-cherry-30: #930006;
  --apl-global-color-red-cherry-40: #C0000B;
  --apl-global-color-red-cherry-50: #E9211E;
  --apl-global-color-red-cherry-60: #FF5546;
  --apl-global-color-red-cherry-70: #FF8A7B;
  --apl-global-color-red-cherry-80: #FFB4AA;
  --apl-global-color-red-cherry-90: #FFDAD5;
  --apl-global-color-red-cherry-95: #FFEDEA;
  --apl-global-color-red-cherry-99: #FFF4F3;
  --apl-global-color-red-cherry-100: #FFFFFF;
  --apl-global-color-yellow-peanut-0: #000000;
  --apl-global-color-yellow-peanut-10: #461B02;
  --apl-global-color-yellow-peanut-20: #7A370D;
  --apl-global-color-yellow-peanut-30: #B26000;
  --apl-global-color-yellow-peanut-40: #D37A03;
  --apl-global-color-yellow-peanut-50: #DF8806;
  --apl-global-color-yellow-peanut-60: #EC9709;
  --apl-global-color-yellow-peanut-70: #F9A60C;
  --apl-global-color-yellow-peanut-80: #FFB828;
  --apl-global-color-yellow-peanut-90: #FFCB59;
  --apl-global-color-yellow-peanut-95: #FFDD86;
  --apl-global-color-yellow-peanut-99: #FFEEB1;
  --apl-global-color-yellow-peanut-100: #FFFFFF;
  --apl-global-color-green-matcha-0: #000000;
  --apl-global-color-green-matcha-10: #233D18;
  --apl-global-color-green-matcha-20: #2F5220;
  --apl-global-color-green-matcha-30: #477A2F;
  --apl-global-color-green-matcha-40: #5EA33F;
  --apl-global-color-green-matcha-50: #76CC4F;
  --apl-global-color-green-matcha-60: #91D672;
  --apl-global-color-green-matcha-70: #ADE095;
  --apl-global-color-green-matcha-80: #C8EBB9;
  --apl-global-color-green-matcha-90: #D6F0CA;
  --apl-global-color-green-matcha-95: #E4F5DC;
  --apl-global-color-green-matcha-99: #F1FAED;
  --apl-global-color-green-matcha-100: #FFFFFF;
  --apl-global-color-lilac-soft-0: #000000;
  --apl-global-color-lilac-soft-10: #3F2745;
  --apl-global-color-lilac-soft-20: #5E4264;
  --apl-global-color-lilac-soft-30: #8C6C94;
  --apl-global-color-lilac-soft-40: #AB87B3;
  --apl-global-color-lilac-soft-50: #CAA3D3;
  --apl-global-color-lilac-soft-60: #D5B5DC;
  --apl-global-color-lilac-soft-70: #DFC8E5;
  --apl-global-color-lilac-soft-80: #EADAED;
  --apl-global-color-lilac-soft-90: #EFE3F2;
  --apl-global-color-lilac-soft-95: #F4EDF6;
  --apl-global-color-lilac-soft-99: #FAF6FB;
  --apl-global-color-lilac-soft-100: #FFFFFF;
  --apl-global-color-black-soft-0: #0000000d;
  --apl-global-color-black-soft-10: #0000001a;
  --apl-global-color-black-soft-20: #00000033;
  --apl-global-color-black-soft-30: #0000004d;
  --apl-global-color-black-soft-40: #00000066;
  --apl-global-color-black-soft-50: #00000080;
  --apl-global-color-black-soft-60: #00000099;
  --apl-global-color-black-soft-70: #000000b3;
  --apl-global-color-black-soft-80: #000000cc;
  --apl-global-color-black-soft-90: #000000e6;
  --apl-global-color-black-soft-100: #000000;
  --apl-global-color-white-soft-0: #FFFFFF0d;
  --apl-global-color-white-soft-10: #FFFFFF1a;
  --apl-global-color-white-soft-20: #FFFFFF33;
  --apl-global-color-white-soft-30: #FFFFFF4d;
  --apl-global-color-white-soft-40: #FFFFFF66;
  --apl-global-color-white-soft-50: #FFFFFF80;
  --apl-global-color-white-soft-60: #FFFFFF99;
  --apl-global-color-white-soft-70: #FFFFFFb3;
  --apl-global-color-white-soft-80: #FFFFFFcc;
  --apl-global-color-white-soft-90: #FFFFFFe6;
  --apl-global-color-white-soft-100: #FFFFFF;
  --apl-global-elevation-x-axis-1: 1px;
  --apl-global-elevation-x-axis-2: 2px;
  --apl-global-elevation-x-axis-4: 4px;
  --apl-global-elevation-x-axis-6: 6px;
  --apl-global-elevation-x-axis-8: 8px;
  --apl-global-elevation-x-axis-10: 10px;
  --apl-global-elevation-x-axis-12: 12px;
  --apl-global-elevation-x-axis-14: 14px;
  --apl-global-elevation-x-axis-16: 16px;
  --apl-global-elevation-x-axis-18: 18px;
  --apl-global-elevation-x-axis-20: 20px;
  --apl-global-elevation-x-axis-22: 22px;
  --apl-global-elevation-x-axis-24: 24px;
  --apl-global-elevation-x-axis-none: 0px;
  --apl-global-elevation-y-axis-1: 1px;
  --apl-global-elevation-y-axis-2: 2px;
  --apl-global-elevation-y-axis-4: 4px;
  --apl-global-elevation-y-axis-6: 6px;
  --apl-global-elevation-y-axis-8: 8px;
  --apl-global-elevation-y-axis-10: 10px;
  --apl-global-elevation-y-axis-12: 12px;
  --apl-global-elevation-y-axis-14: 14px;
  --apl-global-elevation-y-axis-16: 16px;
  --apl-global-elevation-y-axis-18: 18px;
  --apl-global-elevation-y-axis-20: 20px;
  --apl-global-elevation-y-axis-22: 22px;
  --apl-global-elevation-y-axis-24: 24px;
  --apl-global-elevation-y-axis-none: 0px;
  --apl-global-elevation-spread-1: 1px;
  --apl-global-elevation-spread-2: 2px;
  --apl-global-elevation-spread-4: 4px;
  --apl-global-elevation-spread-6: 6px;
  --apl-global-elevation-spread-8: 8px;
  --apl-global-elevation-spread-10: 10px;
  --apl-global-elevation-spread-12: 12px;
  --apl-global-elevation-spread-14: 14px;
  --apl-global-elevation-spread-16: 16px;
  --apl-global-elevation-spread-18: 18px;
  --apl-global-elevation-spread-20: 20px;
  --apl-global-elevation-spread-22: 22px;
  --apl-global-elevation-spread-24: 24px;
  --apl-global-elevation-spread-none: 0px;
  --apl-global-elevation-blur-2: 2px;
  --apl-global-elevation-blur-4: 4px;
  --apl-global-elevation-blur-6: 6px;
  --apl-global-elevation-blur-8: 8px;
  --apl-global-elevation-blur-10: 10px;
  --apl-global-elevation-blur-12: 12px;
  --apl-global-elevation-blur-14: 14px;
  --apl-global-elevation-blur-16: 16px;
  --apl-global-elevation-blur-18: 18px;
  --apl-global-elevation-blur-20: 20px;
  --apl-global-elevation-blur-22: 22px;
  --apl-global-elevation-blur-24: 24px;
  --apl-global-elevation-blur-26: 26px;
  --apl-global-elevation-blur-28: 28px;
  --apl-global-elevation-blur-30: 30px;
  --apl-global-elevation-blur-32: 32px;
  --apl-global-elevation-blur-none: 0px;
  --apl-global-radius-2: 2px;
  --apl-global-radius-4: 4px;
  --apl-global-radius-6: 6px;
  --apl-global-radius-8: 8px;
  --apl-global-radius-10: 10px;
  --apl-global-radius-12: 12px;
  --apl-global-radius-14: 14px;
  --apl-global-radius-16: 16px;
  --apl-global-radius-18: 18px;
  --apl-global-radius-20: 20px;
  --apl-global-radius-22: 22px;
  --apl-global-radius-24: 24px;
  --apl-global-radius-26: 26px;
  --apl-global-radius-28: 28px;
  --apl-global-radius-30: 30px;
  --apl-global-radius-32: 32px;
  --apl-global-radius-34: 34px;
  --apl-global-radius-36: 36px;
  --apl-global-radius-38: 38px;
  --apl-global-radius-40: 40px;
  --apl-global-radius-42: 42px;
  --apl-global-radius-44: 44px;
  --apl-global-radius-46: 46px;
  --apl-global-radius-48: 48px;
  --apl-global-radius-50: 50px;
  --apl-global-radius-52: 52px;
  --apl-global-radius-54: 54px;
  --apl-global-radius-56: 56px;
  --apl-global-radius-58: 58px;
  --apl-global-radius-60: 60px;
  --apl-global-radius-62: 62px;
  --apl-global-radius-64: 64px;
  --apl-global-radius-66: 66px;
  --apl-global-radius-68: 68px;
  --apl-global-radius-70: 70px;
  --apl-global-radius-72: 72px;
  --apl-global-radius-74: 74px;
  --apl-global-radius-76: 76px;
  --apl-global-radius-78: 78px;
  --apl-global-radius-80: 80px;
  --apl-global-radius-none: 0px;
  --apl-global-radius-full: 9999px;
  --apl-global-spacing-2: -2px;
  --apl-global-spacing-4: -4px;
  --apl-global-spacing-6: -6px;
  --apl-global-spacing-8: -8px;
  --apl-global-spacing-10: 10px;
  --apl-global-spacing-12: 12px;
  --apl-global-spacing-14: 14px;
  --apl-global-spacing-16: 16px;
  --apl-global-spacing-18: 18px;
  --apl-global-spacing-20: 20px;
  --apl-global-spacing-21: 21px;
  --apl-global-spacing-22: 22px;
  --apl-global-spacing-24: 24px;
  --apl-global-spacing-26: 26px;
  --apl-global-spacing-28: 28px;
  --apl-global-spacing-30: 30px;
  --apl-global-spacing-32: 32px;
  --apl-global-spacing-34: 34px;
  --apl-global-spacing-36: 36px;
  --apl-global-spacing-38: 38px;
  --apl-global-spacing-40: 40px;
  --apl-global-spacing-42: 42px;
  --apl-global-spacing-44: 44px;
  --apl-global-spacing-46: 46px;
  --apl-global-spacing-48: 48px;
  --apl-global-spacing-50: 50px;
  --apl-global-spacing-52: 52px;
  --apl-global-spacing-54: 54px;
  --apl-global-spacing-56: 56px;
  --apl-global-spacing-58: 58px;
  --apl-global-spacing-60: 60px;
  --apl-global-spacing-62: 62px;
  --apl-global-spacing-64: 64px;
  --apl-global-spacing-66: 66px;
  --apl-global-spacing-68: 68px;
  --apl-global-spacing-70: 70px;
  --apl-global-spacing-72: 72px;
  --apl-global-spacing-74: 74px;
  --apl-global-spacing-76: 76px;
  --apl-global-spacing-78: 78px;
  --apl-global-spacing-80: 80px;
  --apl-global-spacing-82: 82px;
  --apl-global-spacing-84: 84px;
  --apl-global-spacing-86: 86px;
  --apl-global-spacing-88: 88px;
  --apl-global-spacing-90: 90px;
  --apl-global-spacing-92: 92px;
  --apl-global-spacing-94: 94px;
  --apl-global-spacing-96: 96px;
  --apl-global-spacing-98: 98px;
  --apl-global-spacing-100: 100px;
  --apl-global-spacing-102: 102px;
  --apl-global-spacing-104: 104px;
  --apl-global-spacing-106: 106px;
  --apl-global-spacing-108: 108px;
  --apl-global-spacing-110: 110px;
  --apl-global-spacing-112: 112px;
  --apl-global-spacing-114: 114px;
  --apl-global-spacing-116: 116px;
  --apl-global-spacing-118: 118px;
  --apl-global-spacing-120: 120px;
  --apl-global-spacing-122: 122px;
  --apl-global-spacing-124: 124px;
  --apl-global-spacing-126: 126px;
  --apl-global-spacing-128: 128px;
  --apl-global-spacing-none: 0px;
  --apl-global-typography-font-family-ibm-plex-sans-thai: "IBM Plex Sans Thai";
  --apl-global-typography-font-size-4: 4px;
  --apl-global-typography-font-size-6: 6px;
  --apl-global-typography-font-size-8: 8px;
  --apl-global-typography-font-size-10: 10px;
  --apl-global-typography-font-size-12: 12px;
  --apl-global-typography-font-size-14: 14px;
  --apl-global-typography-font-size-15: 15px;
  --apl-global-typography-font-size-16: 16px;
  --apl-global-typography-font-size-18: 18px;
  --apl-global-typography-font-size-20: 20px;
  --apl-global-typography-font-size-21: 21px;
  --apl-global-typography-font-size-22: 22px;
  --apl-global-typography-font-size-24: 24px;
  --apl-global-typography-font-size-26: 26px;
  --apl-global-typography-font-size-27: 27px;
  --apl-global-typography-font-size-28: 28px;
  --apl-global-typography-font-size-30: 30px;
  --apl-global-typography-font-size-32: 32px;
  --apl-global-typography-font-size-33: 33px;
  --apl-global-typography-font-size-34: 34px;
  --apl-global-typography-font-size-36: 36px;
  --apl-global-typography-font-size-38: 38px;
  --apl-global-typography-font-size-40: 40px;
  --apl-global-typography-font-size-42: 42px;
  --apl-global-typography-font-size-44: 44px;
  --apl-global-typography-font-size-45: 45px;
  --apl-global-typography-font-size-46: 46px;
  --apl-global-typography-font-size-47: 47px;
  --apl-global-typography-font-size-48: 48px;
  --apl-global-typography-font-size-50: 50px;
  --apl-global-typography-font-size-52: 52px;
  --apl-global-typography-font-size-54: 54px;
  --apl-global-typography-font-size-56: 56px;
  --apl-global-typography-font-size-57: 57px;
  --apl-global-typography-font-size-58: 58px;
  --apl-global-typography-font-size-60: 60px;
  --apl-global-typography-font-size-62: 62px;
  --apl-global-typography-font-size-64: 64px;
  --apl-global-typography-font-size-66: 66px;
  --apl-global-typography-font-size-68: 68px;
  --apl-global-typography-font-size-70: 70px;
  --apl-global-typography-font-size-72: 72px;
  --apl-global-typography-font-size-74: 74px;
  --apl-global-typography-font-size-76: 76px;
  --apl-global-typography-font-size-78: 78px;
  --apl-global-typography-font-size-80: 80px;
  --apl-global-typography-font-size-82: 82px;
  --apl-global-typography-font-size-84: 84px;
  --apl-global-typography-font-size-85: 85.5px;
  --apl-global-typography-font-size-86: 86px;
  --apl-global-typography-font-size-88: 88px;
  --apl-global-typography-font-size-67_5: 67.5px;
  --apl-global-typography-font-weight-thin-it: 100;
  --apl-global-typography-font-weight-thin: 100;
  --apl-global-typography-font-weight-extra-light: 200;
  --apl-global-typography-font-weight-light: 300;
  --apl-global-typography-font-weight-regular: 400;
  --apl-global-typography-font-weight-medium-it: 500;
  --apl-global-typography-font-weight-medium: 500;
  --apl-global-typography-font-weight-semi-bold: 600;
  --apl-global-typography-font-weight-bold-it: 700;
  --apl-global-typography-font-weight-bold: 700;
  --apl-global-typography-font-weight-black-it: 900;
  --apl-global-typography-font-weight-black: 900;
  --apl-global-typography-font-spacing-4: 4px;
  --apl-global-typography-font-spacing-6: 6px;
  --apl-global-typography-font-spacing-8: 8px;
  --apl-global-typography-font-spacing-10: 10px;
  --apl-global-typography-font-spacing-12: 12px;
  --apl-global-typography-font-spacing-14: 14px;
  --apl-global-typography-font-spacing-16: 16px;
  --apl-global-typography-font-spacing-18: 18px;
  --apl-global-typography-font-spacing-20: 20px;
  --apl-global-typography-font-spacing-22: 22px;
  --apl-global-typography-font-spacing-24: 24px;
  --apl-global-typography-font-spacing-26: 26px;
  --apl-global-typography-font-spacing-28: 28px;
  --apl-global-typography-font-spacing-30: 30px;
  --apl-global-typography-font-spacing-32: 32px;
  --apl-global-typography-font-spacing-34: 34px;
  --apl-global-typography-font-spacing-36: 36px;
  --apl-global-typography-font-spacing-38: 38px;
  --apl-global-typography-font-spacing-40: 40px;
  --apl-global-typography-font-spacing-42: 42px;
  --apl-global-typography-font-spacing-44: 44px;
  --apl-global-typography-font-spacing-46: 46px;
  --apl-global-typography-font-spacing-48: 48px;
  --apl-global-typography-font-spacing-50: 50px;
  --apl-global-typography-font-spacing-52: 52px;
  --apl-global-typography-font-spacing-54: 54px;
  --apl-global-typography-font-spacing-56: 56px;
  --apl-global-typography-font-spacing-58: 58px;
  --apl-global-typography-font-spacing-60: 60px;
  --apl-global-typography-font-spacing-62: 62px;
  --apl-global-typography-font-spacing-64: 64px;
  --apl-global-typography-font-spacing-66: 66px;
  --apl-global-typography-font-spacing-68: 68px;
  --apl-global-typography-font-spacing-70: 70px;
  --apl-global-typography-font-spacing-72: 72px;
  --apl-global-typography-font-spacing-74: 74px;
  --apl-global-typography-font-spacing-76: 76px;
  --apl-global-typography-font-spacing-78: 78px;
  --apl-global-typography-font-spacing-80: 80px;
  --apl-global-typography-font-spacing-82: 82px;
  --apl-global-typography-font-spacing-84: 84px;
  --apl-global-typography-font-spacing-86: 86px;
  --apl-global-typography-font-spacing-88: 88px;
  --apl-global-typography-line-height-4: 4;
  --apl-global-typography-line-height-6: 6;
  --apl-global-typography-line-height-8: 8;
  --apl-global-typography-line-height-10: 10;
  --apl-global-typography-line-height-12: 12;
  --apl-global-typography-line-height-14: 14;
  --apl-global-typography-line-height-15: 15;
  --apl-global-typography-line-height-16: 16;
  --apl-global-typography-line-height-18: 18;
  --apl-global-typography-line-height-20: 20;
  --apl-global-typography-line-height-21: 21;
  --apl-global-typography-line-height-22: 22;
  --apl-global-typography-line-height-24: 24;
  --apl-global-typography-line-height-26: 26;
  --apl-global-typography-line-height-27: 27;
  --apl-global-typography-line-height-28: 28;
  --apl-global-typography-line-height-30: 30;
  --apl-global-typography-line-height-32: 32;
  --apl-global-typography-line-height-33: 33;
  --apl-global-typography-line-height-34: 34;
  --apl-global-typography-line-height-36: 36;
  --apl-global-typography-line-height-38: 38;
  --apl-global-typography-line-height-40: 40;
  --apl-global-typography-line-height-42: 42;
  --apl-global-typography-line-height-44: 44;
  --apl-global-typography-line-height-46: 46;
  --apl-global-typography-line-height-48: 48;
  --apl-global-typography-line-height-50: 50;
  --apl-global-typography-line-height-52: 52;
  --apl-global-typography-line-height-54: 54;
  --apl-global-typography-line-height-56: 56;
  --apl-global-typography-line-height-58: 58;
  --apl-global-typography-line-height-60: 60;
  --apl-global-typography-line-height-62: 62;
  --apl-global-typography-line-height-64: 64;
  --apl-global-typography-line-height-66: 66;
  --apl-global-typography-line-height-67: 67.5;
  --apl-global-typography-line-height-68: 68;
  --apl-global-typography-line-height-70: 70;
  --apl-global-typography-line-height-72: 72;
  --apl-global-typography-line-height-74: 74;
  --apl-global-typography-line-height-76: 76;
  --apl-global-typography-line-height-78: 78;
  --apl-global-typography-line-height-80: 80;
  --apl-global-typography-line-height-82: 82;
  --apl-global-typography-line-height-84: 84;
  --apl-global-typography-line-height-85: 85.5;
  --apl-global-typography-line-height-86: 86;
  --apl-global-typography-line-height-88: 88;
  --apl-global-typography-line-height-96: 96;
  --apl-global-typography-line-height-132: 132;
  --apl-base-color-primary-0: var(--apl-global-color-green-pine-0);
  --apl-base-color-primary-10: var(--apl-global-color-green-pine-10);
  --apl-base-color-primary-20: var(--apl-global-color-green-pine-20);
  --apl-base-color-primary-30: var(--apl-global-color-green-pine-30);
  --apl-base-color-primary-40: var(--apl-global-color-green-pine-40);
  --apl-base-color-primary-50: var(--apl-global-color-green-pine-50);
  --apl-base-color-primary-60: var(--apl-global-color-green-pine-60);
  --apl-base-color-primary-70: var(--apl-global-color-green-pine-70);
  --apl-base-color-primary-80: var(--apl-global-color-green-pine-80);
  --apl-base-color-primary-90: var(--apl-global-color-green-pine-90);
  --apl-base-color-primary-95: var(--apl-global-color-green-pine-95);
  --apl-base-color-primary-99: var(--apl-global-color-green-pine-99);
  --apl-base-color-primary-100: var(--apl-global-color-green-pine-100);
  --apl-base-color-secondary-0: var(--apl-global-color-gray-bluish-0);
  --apl-base-color-secondary-10: var(--apl-global-color-gray-bluish-10);
  --apl-base-color-secondary-20: var(--apl-global-color-gray-bluish-20);
  --apl-base-color-secondary-30: var(--apl-global-color-gray-bluish-30);
  --apl-base-color-secondary-40: var(--apl-global-color-gray-bluish-40);
  --apl-base-color-secondary-50: var(--apl-global-color-gray-bluish-50);
  --apl-base-color-secondary-60: var(--apl-global-color-gray-bluish-60);
  --apl-base-color-secondary-70: var(--apl-global-color-gray-bluish-70);
  --apl-base-color-secondary-80: var(--apl-global-color-gray-bluish-80);
  --apl-base-color-secondary-90: var(--apl-global-color-gray-bluish-90);
  --apl-base-color-secondary-95: var(--apl-global-color-gray-bluish-95);
  --apl-base-color-secondary-99: var(--apl-global-color-gray-bluish-99);
  --apl-base-color-secondary-100: var(--apl-global-color-gray-bluish-100);
  --apl-base-color-tertiary-0: var(--apl-global-color-blue-ocean-0);
  --apl-base-color-tertiary-10: var(--apl-global-color-blue-ocean-10);
  --apl-base-color-tertiary-20: var(--apl-global-color-blue-ocean-20);
  --apl-base-color-tertiary-30: var(--apl-global-color-blue-ocean-30);
  --apl-base-color-tertiary-40: var(--apl-global-color-blue-ocean-40);
  --apl-base-color-tertiary-50: var(--apl-global-color-blue-ocean-50);
  --apl-base-color-tertiary-60: var(--apl-global-color-blue-ocean-60);
  --apl-base-color-tertiary-70: var(--apl-global-color-blue-ocean-70);
  --apl-base-color-tertiary-80: var(--apl-global-color-blue-ocean-80);
  --apl-base-color-tertiary-90: var(--apl-global-color-blue-ocean-90);
  --apl-base-color-tertiary-95: var(--apl-global-color-blue-ocean-95);
  --apl-base-color-tertiary-99: var(--apl-global-color-blue-ocean-99);
  --apl-base-color-tertiary-100: var(--apl-global-color-blue-ocean-100);
  --apl-base-color-neutral-0: var(--apl-global-color-gray-smoke-0);
  --apl-base-color-neutral-10: var(--apl-global-color-gray-smoke-10);
  --apl-base-color-neutral-20: var(--apl-global-color-gray-smoke-20);
  --apl-base-color-neutral-30: var(--apl-global-color-gray-smoke-30);
  --apl-base-color-neutral-40: var(--apl-global-color-gray-smoke-40);
  --apl-base-color-neutral-50: var(--apl-global-color-gray-smoke-50);
  --apl-base-color-neutral-60: var(--apl-global-color-gray-smoke-60);
  --apl-base-color-neutral-70: var(--apl-global-color-gray-smoke-70);
  --apl-base-color-neutral-80: var(--apl-global-color-gray-smoke-80);
  --apl-base-color-neutral-90: var(--apl-global-color-gray-smoke-90);
  --apl-base-color-neutral-95: var(--apl-global-color-gray-smoke-95);
  --apl-base-color-neutral-99: var(--apl-global-color-gray-smoke-99);
  --apl-base-color-neutral-100: var(--apl-global-color-gray-smoke-100);
  --apl-base-color-danger-0: var(--apl-global-color-red-cherry-0);
  --apl-base-color-danger-10: var(--apl-global-color-red-cherry-10);
  --apl-base-color-danger-20: var(--apl-global-color-red-cherry-20);
  --apl-base-color-danger-30: var(--apl-global-color-red-cherry-30);
  --apl-base-color-danger-40: var(--apl-global-color-red-cherry-40);
  --apl-base-color-danger-50: var(--apl-global-color-red-cherry-50);
  --apl-base-color-danger-60: var(--apl-global-color-red-cherry-60);
  --apl-base-color-danger-70: var(--apl-global-color-red-cherry-70);
  --apl-base-color-danger-80: var(--apl-global-color-red-cherry-80);
  --apl-base-color-danger-90: var(--apl-global-color-red-cherry-90);
  --apl-base-color-danger-95: var(--apl-global-color-red-cherry-95);
  --apl-base-color-danger-99: var(--apl-global-color-red-cherry-99);
  --apl-base-color-danger-100: var(--apl-global-color-red-cherry-100);
  --apl-base-color-warning-0: var(--apl-global-color-yellow-peanut-0);
  --apl-base-color-warning-10: var(--apl-global-color-yellow-peanut-10);
  --apl-base-color-warning-20: var(--apl-global-color-yellow-peanut-20);
  --apl-base-color-warning-30: var(--apl-global-color-yellow-peanut-30);
  --apl-base-color-warning-40: var(--apl-global-color-yellow-peanut-40);
  --apl-base-color-warning-50: var(--apl-global-color-yellow-peanut-50);
  --apl-base-color-warning-60: var(--apl-global-color-yellow-peanut-60);
  --apl-base-color-warning-70: var(--apl-global-color-yellow-peanut-70);
  --apl-base-color-warning-80: var(--apl-global-color-yellow-peanut-80);
  --apl-base-color-warning-90: var(--apl-global-color-yellow-peanut-90);
  --apl-base-color-warning-95: var(--apl-global-color-yellow-peanut-95);
  --apl-base-color-warning-99: var(--apl-global-color-yellow-peanut-99);
  --apl-base-color-warning-100: var(--apl-global-color-yellow-peanut-100);
  --apl-base-color-success-0: var(--apl-global-color-green-matcha-0);
  --apl-base-color-success-10: var(--apl-global-color-green-matcha-10);
  --apl-base-color-success-20: var(--apl-global-color-green-matcha-20);
  --apl-base-color-success-30: var(--apl-global-color-green-matcha-30);
  --apl-base-color-success-40: var(--apl-global-color-green-matcha-40);
  --apl-base-color-success-50: var(--apl-global-color-green-matcha-50);
  --apl-base-color-success-60: var(--apl-global-color-green-matcha-60);
  --apl-base-color-success-70: var(--apl-global-color-green-matcha-70);
  --apl-base-color-success-80: var(--apl-global-color-green-matcha-80);
  --apl-base-color-success-90: var(--apl-global-color-green-matcha-90);
  --apl-base-color-success-95: var(--apl-global-color-green-matcha-95);
  --apl-base-color-success-99: var(--apl-global-color-green-matcha-99);
  --apl-base-color-success-100: var(--apl-global-color-green-matcha-100);
  --apl-base-color-overlay-black-0: var(--apl-global-color-black-soft-0);
  --apl-base-color-overlay-black-10: var(--apl-global-color-black-soft-10);
  --apl-base-color-overlay-black-20: var(--apl-global-color-black-soft-20);
  --apl-base-color-overlay-black-30: var(--apl-global-color-black-soft-30);
  --apl-base-color-overlay-black-40: var(--apl-global-color-black-soft-40);
  --apl-base-color-overlay-black-50: var(--apl-global-color-black-soft-50);
  --apl-base-color-overlay-black-60: var(--apl-global-color-black-soft-60);
  --apl-base-color-overlay-black-70: var(--apl-global-color-black-soft-70);
  --apl-base-color-overlay-black-80: var(--apl-global-color-black-soft-80);
  --apl-base-color-overlay-black-90: var(--apl-global-color-black-soft-90);
  --apl-base-color-overlay-black-100: var(--apl-global-color-black-soft-100);
  --apl-base-color-overlay-white-0: var(--apl-global-color-white-soft-0);
  --apl-base-color-overlay-white-10: var(--apl-global-color-white-soft-10);
  --apl-base-color-overlay-white-20: var(--apl-global-color-white-soft-20);
  --apl-base-color-overlay-white-30: var(--apl-global-color-white-soft-30);
  --apl-base-color-overlay-white-40: var(--apl-global-color-white-soft-40);
  --apl-base-color-overlay-white-50: var(--apl-global-color-white-soft-50);
  --apl-base-color-overlay-white-60: var(--apl-global-color-white-soft-60);
  --apl-base-color-overlay-white-70: var(--apl-global-color-white-soft-70);
  --apl-base-color-overlay-white-80: var(--apl-global-color-white-soft-80);
  --apl-base-color-overlay-white-90: var(--apl-global-color-white-soft-90);
  --apl-base-color-overlay-white-100: var(--apl-global-color-white-soft-100);
  --apl-base-color-kid-club-0: var(--apl-global-color-lilac-soft-0);
  --apl-base-color-kid-club-color: #FFFFFF;
  --apl-base-color-kid-club-color-2: #FFFFFF;
  --apl-base-color-kid-club-color-3: #FFFFFF;
  --apl-base-color-kid-club-color-4: #FFFFFF;
  --apl-base-color-kid-club-color-5: #FFFFFF;
  --apl-base-color-kid-club-color-6: #FFFFFF;
  --apl-base-color-kid-club-color-7: #FFFFFF;
  --apl-base-color-kid-club-color-8: #FFFFFF;
  --apl-base-color-kid-club-color-9: #FFFFFF;
  --apl-base-color-kid-club-color-10: #FFFFFF;
  --apl-base-color-kid-club-color-11: #FFFFFF;
  --apl-base-color-kid-club-color-12: #FFFFFF;
  --apl-base-elevation-x-axis-1: var(--apl-global-elevation-x-axis-1);
  --apl-base-elevation-x-axis-2: var(--apl-global-elevation-x-axis-2);
  --apl-base-elevation-x-axis-4: var(--apl-global-elevation-x-axis-4);
  --apl-base-elevation-x-axis-6: var(--apl-global-elevation-x-axis-6);
  --apl-base-elevation-x-axis-8: var(--apl-global-elevation-x-axis-8);
  --apl-base-elevation-x-axis-10: var(--apl-global-elevation-x-axis-10);
  --apl-base-elevation-x-axis-12: var(--apl-global-elevation-x-axis-12);
  --apl-base-elevation-x-axis-14: var(--apl-global-elevation-x-axis-14);
  --apl-base-elevation-x-axis-16: var(--apl-global-elevation-x-axis-16);
  --apl-base-elevation-x-axis-18: var(--apl-global-elevation-x-axis-18);
  --apl-base-elevation-x-axis-20: var(--apl-global-elevation-x-axis-20);
  --apl-base-elevation-x-axis-22: var(--apl-global-elevation-y-axis-22);
  --apl-base-elevation-x-axis-24: var(--apl-global-elevation-x-axis-24);
  --apl-base-elevation-x-axis-none: var(--apl-global-elevation-x-axis-none);
  --apl-base-elevation-y-axis-1: var(--apl-global-elevation-y-axis-1);
  --apl-base-elevation-y-axis-2: var(--apl-global-elevation-y-axis-2);
  --apl-base-elevation-y-axis-4: var(--apl-global-elevation-y-axis-4);
  --apl-base-elevation-y-axis-6: var(--apl-global-elevation-y-axis-6);
  --apl-base-elevation-y-axis-8: var(--apl-global-elevation-y-axis-8);
  --apl-base-elevation-y-axis-10: var(--apl-global-elevation-y-axis-10);
  --apl-base-elevation-y-axis-12: var(--apl-global-elevation-y-axis-12);
  --apl-base-elevation-y-axis-14: var(--apl-global-elevation-y-axis-14);
  --apl-base-elevation-y-axis-16: var(--apl-global-elevation-y-axis-16);
  --apl-base-elevation-y-axis-18: var(--apl-global-elevation-y-axis-18);
  --apl-base-elevation-y-axis-20: var(--apl-global-elevation-y-axis-20);
  --apl-base-elevation-y-axis-22: var(--apl-global-elevation-y-axis-22);
  --apl-base-elevation-y-axis-24: var(--apl-global-elevation-y-axis-24);
  --apl-base-elevation-y-axis-none: var(--apl-global-elevation-y-axis-none);
  --apl-base-elevation-spread-1: var(--apl-global-elevation-spread-1);
  --apl-base-elevation-spread-2: var(--apl-global-elevation-spread-2);
  --apl-base-elevation-spread-4: var(--apl-global-elevation-spread-4);
  --apl-base-elevation-spread-6: var(--apl-global-elevation-spread-8);
  --apl-base-elevation-spread-8: var(--apl-global-elevation-spread-8);
  --apl-base-elevation-spread-10: var(--apl-global-elevation-spread-10);
  --apl-base-elevation-spread-12: var(--apl-global-elevation-spread-12);
  --apl-base-elevation-spread-14: var(--apl-global-elevation-spread-14);
  --apl-base-elevation-spread-16: var(--apl-global-elevation-spread-16);
  --apl-base-elevation-spread-18: var(--apl-global-elevation-spread-18);
  --apl-base-elevation-spread-20: var(--apl-global-elevation-spread-20);
  --apl-base-elevation-spread-22: var(--apl-global-elevation-spread-22);
  --apl-base-elevation-spread-24: var(--apl-global-elevation-spread-24);
  --apl-base-elevation-spread-none: var(--apl-global-elevation-spread-none);
  --apl-base-elevation-blur-2: var(--apl-global-elevation-blur-2);
  --apl-base-elevation-blur-4: var(--apl-global-elevation-blur-4);
  --apl-base-elevation-blur-6: var(--apl-global-elevation-blur-6);
  --apl-base-elevation-blur-8: var(--apl-global-elevation-blur-8);
  --apl-base-elevation-blur-10: var(--apl-global-elevation-blur-10);
  --apl-base-elevation-blur-12: var(--apl-global-elevation-blur-12);
  --apl-base-elevation-blur-14: var(--apl-global-elevation-blur-14);
  --apl-base-elevation-blur-16: var(--apl-global-elevation-blur-16);
  --apl-base-elevation-blur-18: var(--apl-global-elevation-blur-18);
  --apl-base-elevation-blur-20: var(--apl-global-elevation-blur-20);
  --apl-base-elevation-blur-22: var(--apl-global-elevation-blur-22);
  --apl-base-elevation-blur-24: var(--apl-global-elevation-blur-24);
  --apl-base-elevation-blur-26: var(--apl-global-elevation-blur-26);
  --apl-base-elevation-blur-28: var(--apl-global-elevation-blur-28);
  --apl-base-elevation-blur-30: var(--apl-global-elevation-blur-30);
  --apl-base-elevation-blur-32: var(--apl-global-elevation-blur-32);
  --apl-base-elevation-blur-none: var(--apl-global-elevation-blur-none);
  --apl-base-radius-2: var(--apl-global-radius-2);
  --apl-base-radius-4: var(--apl-global-radius-4);
  --apl-base-radius-6: var(--apl-global-radius-6);
  --apl-base-radius-8: var(--apl-global-radius-8);
  --apl-base-radius-10: var(--apl-global-radius-10);
  --apl-base-radius-12: var(--apl-global-radius-12);
  --apl-base-radius-14: var(--apl-global-radius-14);
  --apl-base-radius-16: var(--apl-global-radius-16);
  --apl-base-radius-18: var(--apl-global-radius-18);
  --apl-base-radius-20: var(--apl-global-radius-20);
  --apl-base-radius-22: var(--apl-global-radius-22);
  --apl-base-radius-24: var(--apl-global-radius-24);
  --apl-base-radius-26: var(--apl-global-radius-26);
  --apl-base-radius-28: var(--apl-global-radius-28);
  --apl-base-radius-30: var(--apl-global-radius-30);
  --apl-base-radius-32: var(--apl-global-radius-32);
  --apl-base-radius-34: var(--apl-global-radius-34);
  --apl-base-radius-36: var(--apl-global-radius-36);
  --apl-base-radius-38: var(--apl-global-radius-38);
  --apl-base-radius-40: var(--apl-global-radius-40);
  --apl-base-radius-42: var(--apl-global-radius-42);
  --apl-base-radius-44: var(--apl-global-radius-44);
  --apl-base-radius-46: var(--apl-global-radius-46);
  --apl-base-radius-48: var(--apl-global-radius-48);
  --apl-base-radius-50: var(--apl-global-radius-50);
  --apl-base-radius-52: var(--apl-global-radius-52);
  --apl-base-radius-54: var(--apl-global-radius-54);
  --apl-base-radius-56: var(--apl-global-radius-56);
  --apl-base-radius-58: var(--apl-global-radius-58);
  --apl-base-radius-60: var(--apl-global-radius-60);
  --apl-base-radius-62: var(--apl-global-radius-62);
  --apl-base-radius-64: var(--apl-global-radius-64);
  --apl-base-radius-66: var(--apl-global-radius-66);
  --apl-base-radius-68: var(--apl-global-radius-68);
  --apl-base-radius-70: var(--apl-global-radius-70);
  --apl-base-radius-72: var(--apl-global-radius-72);
  --apl-base-radius-74: var(--apl-global-radius-74);
  --apl-base-radius-76: var(--apl-global-radius-76);
  --apl-base-radius-78: var(--apl-global-radius-78);
  --apl-base-radius-80: var(--apl-global-radius-80);
  --apl-base-radius-none: var(--apl-global-radius-none);
  --apl-base-radius-full: var(--apl-global-radius-full);
  --apl-base-spacing-space1: var(--apl-global-spacing-none);
  --apl-base-spacing-space2: var(--apl-global-spacing-2);
  --apl-base-spacing-space3: var(--apl-global-spacing-4);
  --apl-base-spacing-space4: var(--apl-global-spacing-6);
  --apl-base-spacing-space5: var(--apl-global-spacing-8);
  --apl-base-spacing-space6: var(--apl-global-spacing-10);
  --apl-base-spacing-space7: var(--apl-global-spacing-12);
  --apl-base-spacing-space8: var(--apl-global-spacing-16);
  --apl-base-spacing-space9: var(--apl-global-spacing-20);
  --apl-base-spacing-space10: var(--apl-global-spacing-24);
  --apl-base-spacing-space11: var(--apl-global-spacing-28);
  --apl-base-spacing-space12: var(--apl-global-spacing-32);
  --apl-base-spacing-space13: var(--apl-global-spacing-36);
  --apl-base-spacing-space14: var(--apl-global-spacing-40);
  --apl-base-spacing-space15: var(--apl-global-spacing-48);
  --apl-base-spacing-neg-space1: var(--apl-global-spacing-2);
  --apl-base-spacing-neg-space2: var(--apl-global-spacing-4);
  --apl-base-spacing-neg-space3: var(--apl-global-spacing-6);
  --apl-base-spacing-neg-space4: var(--apl-global-spacing-8);
  --apl-base-spacing-alias-color-schemes-tertiary-tertiary: var(--apl-base-color-tertiary-40);
  --apl-base-spacing-title-large-font-size: var(--apl-global-spacing-font-scale-scale8);
  --apl-base-spacing-title-large-line-height: var(--apl-global-spacing-line-height-height8);
  --apl-base-spacing-title-large-letter-spacing: var(--apl-global-spacing-letter-spacing-letter3);
  --apl-base-typography-font-family: var(--apl-global-typography-font-family-ibm-plex-sans-thai);
  --apl-base-typography-font-size-3xl: var(--apl-global-typography-font-size-64);
  --apl-base-typography-font-size-2xl: var(--apl-global-typography-font-size-60);
  --apl-base-typography-font-size-xl: var(--apl-global-typography-font-size-56);
  --apl-base-typography-font-size-lg: var(--apl-global-typography-font-size-52);
  --apl-base-typography-font-size-md: var(--apl-global-typography-font-size-48);
  --apl-base-typography-font-size-sm: var(--apl-global-typography-font-size-44);
  --apl-base-typography-font-size-xs-plus: var(--apl-global-typography-font-size-40);
  --apl-base-typography-font-size-xs-md: var(--apl-global-typography-font-size-36);
  --apl-base-typography-font-size-xs-sm: var(--apl-global-typography-font-size-32);
  --apl-base-typography-font-size-xs: var(--apl-global-typography-font-size-28);
  --apl-base-typography-font-size-2xs: var(--apl-global-typography-font-size-24);
  --apl-base-typography-font-size-3xs: var(--apl-global-typography-font-size-20);
  --apl-base-typography-font-size-4xs: var(--apl-global-typography-font-size-16);
  --apl-base-typography-font-weight-weight1: var(--apl-global-typography-font-weight-regular);
  --apl-base-typography-font-weight-weight2: var(--apl-global-typography-font-weight-medium);
  --apl-base-typography-font-weight-weight3: var(--apl-global-typography-font-weight-bold);
  --apl-base-typography-line-height-line-height1: var(--apl-global-typography-line-height-4);
  --apl-base-typography-line-height-line-height2: var(--apl-global-typography-line-height-6);
  --apl-base-typography-line-height-line-height3: var(--apl-global-typography-line-height-8);
  --apl-base-typography-line-height-line-height4: var(--apl-global-typography-line-height-10);
  --apl-base-typography-line-height-line-height5: var(--apl-global-typography-line-height-12);
  --apl-base-typography-line-height-line-height6: var(--apl-global-typography-line-height-14);
  --apl-base-typography-line-height-line-height7: var(--apl-global-typography-line-height-15);
  --apl-base-typography-line-height-line-height8: var(--apl-global-typography-line-height-16);
  --apl-base-typography-line-height-line-height9: var(--apl-global-typography-line-height-18);
  --apl-base-typography-line-height-line-height10: var(--apl-global-typography-line-height-20);
  --apl-base-typography-line-height-line-height11: var(--apl-global-typography-line-height-21);
  --apl-base-typography-line-height-line-height12: var(--apl-global-typography-line-height-22);
  --apl-base-typography-line-height-line-height13: var(--apl-global-typography-line-height-24);
  --apl-base-typography-line-height-line-height14: var(--apl-global-typography-line-height-26);
  --apl-base-typography-line-height-line-height15: var(--apl-global-typography-line-height-27);
  --apl-base-typography-line-height-line-height16: var(--apl-global-typography-line-height-28);
  --apl-base-typography-line-height-line-height17: var(--apl-global-typography-line-height-30);
  --apl-base-typography-line-height-line-height18: var(--apl-global-typography-line-height-32);
  --apl-base-typography-line-height-line-height19: var(--apl-global-typography-line-height-33);
  --apl-base-typography-line-height-line-height20: var(--apl-global-typography-line-height-34);
  --apl-base-typography-line-height-line-height21: var(--apl-global-typography-line-height-36);
  --apl-base-typography-line-height-line-height22: var(--apl-global-typography-line-height-38);
  --apl-base-typography-line-height-line-height23: var(--apl-global-typography-line-height-40);
  --apl-base-typography-line-height-line-height24: var(--apl-global-typography-line-height-42);
  --apl-base-typography-line-height-line-height25: var(--apl-global-typography-line-height-44);
  --apl-base-typography-line-height-line-height26: var(--apl-global-typography-line-height-46);
  --apl-base-typography-line-height-line-height27: var(--apl-global-typography-line-height-48);
  --apl-base-typography-line-height-line-height28: var(--apl-global-typography-line-height-50);
  --apl-base-typography-line-height-line-height29: var(--apl-global-typography-line-height-52);
  --apl-base-typography-line-height-line-height30: var(--apl-global-typography-line-height-54);
  --apl-base-typography-line-height-line-height31: var(--apl-global-typography-line-height-56);
  --apl-base-typography-line-height-line-height32: var(--apl-global-typography-line-height-58);
  --apl-base-typography-line-height-line-height33: var(--apl-global-typography-line-height-60);
  --apl-base-typography-line-height-line-height34: var(--apl-global-typography-line-height-62);
  --apl-base-typography-line-height-line-height35: var(--apl-global-typography-line-height-64);
  --apl-base-typography-line-height-line-height36: var(--apl-global-typography-line-height-66);
  --apl-base-typography-line-height-line-height37: var(--apl-global-typography-line-height-67);
  --apl-base-typography-line-height-line-height38: var(--apl-global-typography-line-height-68);
  --apl-base-typography-line-height-line-height39: var(--apl-global-typography-line-height-70);
  --apl-base-typography-line-height-line-height40: var(--apl-global-typography-line-height-72);
  --apl-base-typography-line-height-line-height41: var(--apl-global-typography-line-height-74);
  --apl-base-typography-line-height-line-height42: var(--apl-global-typography-line-height-76);
  --apl-base-typography-line-height-line-height43: var(--apl-global-typography-line-height-78);
  --apl-base-typography-line-height-line-height44: var(--apl-global-typography-line-height-80);
  --apl-base-typography-line-height-line-height45: var(--apl-global-typography-line-height-82);
  --apl-base-typography-line-height-line-height46: var(--apl-global-typography-line-height-84);
  --apl-base-typography-line-height-line-height47: var(--apl-global-typography-line-height-85);
  --apl-base-typography-line-height-line-height48: var(--apl-global-typography-line-height-86);
  --apl-base-typography-line-height-line-height49: var(--apl-global-typography-line-height-88);
  --apl-base-typography-line-height-line-height50: var(--apl-global-typography-line-height-96);
  --apl-base-typography-line-height-line-height51: var(--apl-global-typography-line-height-132);
  --apl-alias-color-primary-primary: var(--apl-base-color-primary-40);
  --apl-alias-color-primary-surface-tint: var(--apl-base-color-primary-40);
  --apl-alias-color-primary-on-primary: var(--apl-base-color-primary-100);
  --apl-alias-color-primary-primary-container: var(--apl-base-color-primary-95);
  --apl-alias-color-error-error: var(--apl-base-color-danger-40);
  --apl-alias-color-error-on-error: var(--apl-base-color-danger-100);
  --apl-alias-color-error-error-container: var(--apl-base-color-danger-95);
  --apl-alias-color-error-on-error-container: var(--apl-base-color-danger-30);
  --apl-alias-color-warning-warning: var(--apl-base-color-warning-50);
  --apl-alias-color-warning-on-warning: var(--apl-base-color-warning-100);
  --apl-alias-color-warning-warning-container: var(--apl-base-color-warning-95);
  --apl-alias-color-warning-on-warning-container: var(--apl-base-color-warning-30);
  --apl-alias-color-secondary-seconday: var(--apl-base-color-secondary-40);
  --apl-alias-color-secondary-on-secondary: var(--apl-base-color-secondary-100);
  --apl-alias-color-secondary-secondary-container: var(--apl-base-color-secondary-95);
  --apl-alias-color-secondary-on-secondary-container: var(--apl-base-color-secondary-30);
  --apl-alias-color-tertiary-tertiary: var(--apl-base-color-tertiary-40);
  --apl-alias-color-tertiary-on-tertiary: var(--apl-base-color-tertiary-100);
  --apl-alias-color-tertiary-tertiary-container: var(--apl-base-color-tertiary-95);
  --apl-alias-color-tertiary-on-tertiary-container: var(--apl-base-color-tertiary-30);
  --apl-alias-color-background-and-surface-background: var(--apl-base-color-neutral-100);
  --apl-alias-color-background-and-surface-on-background: var(--apl-base-color-neutral-99);
  --apl-alias-color-background-and-surface-surface: var(--apl-base-color-neutral-99);
  --apl-alias-color-background-and-surface-on-surface: var(--apl-base-color-neutral-30);
  --apl-alias-color-background-and-surface-surface-variant: var(--apl-base-color-neutral-100);
  --apl-alias-color-background-and-surface-on-surface-variant: var(--apl-base-color-neutral-50);
  --apl-alias-color-outline-and-border-outline: var(--apl-base-color-neutral-70);
  --apl-alias-color-outline-and-border-outline-variant: var(--apl-base-color-neutral-80);
  --apl-alias-color-outline-and-border-border: var(--apl-base-color-neutral-30);
  --apl-alias-color-effects-shadow: var(--apl-base-color-overlay-black-40);
  --apl-alias-color-effects-scrim: var(--apl-base-color-overlay-black-40);
  --apl-alias-color-success-success: var(--apl-base-color-success-50);
  --apl-alias-color-success-on-success: var(--apl-base-color-success-100);
  --apl-alias-color-success-success-container: var(--apl-base-color-success-95);
  --apl-alias-color-success-on-success-container: var(--apl-base-color-success-30);
  --apl-alias-elevation-elevations1-x-axis: var(--apl-base-elevation-x-axis-none);
  --apl-alias-elevation-elevations1-y-axis: var(--apl-base-elevation-y-axis-2);
  --apl-alias-elevation-elevations1-blur: var(--apl-base-elevation-blur-4);
  --apl-alias-elevation-elevations1-spread: var(--apl-base-spacing-space1);
  --apl-alias-elevation-elevations1-color: var(--apl-base-color-overlay-black-10);
  --apl-alias-elevation-elevations2-x-axis: 0px;
  --apl-alias-elevation-elevations2-y-axis: 0px;
  --apl-alias-elevation-elevations2-blur: 0px;
  --apl-alias-elevation-elevations2-spread: 0px;
  --apl-alias-elevation-elevations2-color: 0px;
  --apl-alias-elevation-elevations3-x-axis: 0px;
  --apl-alias-elevation-elevations3-y-axis: 0px;
  --apl-alias-elevation-elevations3-blur: 0px;
  --apl-alias-elevation-elevations3-spread: 0px;
  --apl-alias-elevation-elevations3-color: 0px;
  --apl-alias-radius-radius1: var(--apl-base-radius-none);
  --apl-alias-radius-radius2: var(--apl-base-radius-4);
  --apl-alias-radius-radius3: var(--apl-base-radius-6);
  --apl-alias-radius-radius4: var(--apl-base-radius-8);
  --apl-alias-radius-radius5: var(--apl-base-radius-10);
  --apl-alias-radius-radius6: var(--apl-base-radius-12);
  --apl-alias-radius-radius7: var(--apl-base-radius-14);
  --apl-alias-radius-radius8: var(--apl-base-radius-16);
  --apl-alias-radius-radius9: var(--apl-base-radius-20);
  --apl-alias-radius-radius10: var(--apl-base-radius-24);
  --apl-alias-radius-radius11: var(--apl-base-radius-full);
  --apl-alias-spacing-margin-vertical-vertical: var(--apl-base-spacing-space8);
  --apl-alias-spacing-margin-horizontal-horizontal: var(--apl-base-spacing-space8);
  --apl-alias-spacing-padding-padding1: var(--apl-base-spacing-space1);
  --apl-alias-spacing-padding-padding2: var(--apl-base-spacing-space2);
  --apl-alias-spacing-padding-padding3: var(--apl-base-spacing-space3);
  --apl-alias-spacing-padding-padding4: var(--apl-base-spacing-space4);
  --apl-alias-spacing-padding-padding5: var(--apl-base-spacing-space6);
  --apl-alias-spacing-padding-padding6: var(--apl-base-spacing-space6);
  --apl-alias-spacing-padding-padding7: var(--apl-base-spacing-space7);
  --apl-alias-spacing-padding-padding8: var(--apl-base-spacing-space8);
  --apl-alias-spacing-padding-padding9: var(--apl-base-spacing-space9);
  --apl-alias-spacing-padding-padding10: var(--apl-base-spacing-space10);
  --apl-alias-spacing-padding-padding11: var(--apl-base-spacing-space11);
  --apl-alias-spacing-padding-padding12: var(--apl-base-spacing-space12);
  --apl-alias-spacing-gap-gap1: var(--apl-base-spacing-space1);
  --apl-alias-spacing-gap-gap2: var(--apl-base-spacing-space2);
  --apl-alias-spacing-gap-gap3: var(--apl-base-spacing-space3);
  --apl-alias-spacing-gap-gap4: var(--apl-base-spacing-space4);
  --apl-alias-spacing-gap-gap5: var(--apl-base-spacing-space5);
  --apl-alias-spacing-gap-gap6: var(--apl-base-spacing-space6);
  --apl-alias-spacing-gap-gap7: var(--apl-base-spacing-space7);
  --apl-alias-spacing-gap-gap8: var(--apl-base-spacing-space8);
  --apl-alias-spacing-gap-gap9: var(--apl-base-spacing-space9);
  --apl-alias-spacing-gap-gap10: var(--apl-base-spacing-space10);
  --apl-alias-spacing-gap-gap11: var(--apl-base-spacing-space11);
  --apl-alias-spacing-gap-gap12: var(--apl-base-spacing-space12);
  --apl-alias-typography-font-family: var(--apl-base-typography-font-family-ibm-plex-sans-thai);
  --apl-alias-typography-display-large-font-family: var(--apl-base-typography-font-family-ibm-plex-sans-thai);
  --apl-alias-typography-display-large-font-weight: var(--apl-base-typography-font-weight-weight3);
  --apl-alias-typography-display-large-font-size: var(--apl-base-typography-font-size-3xl);
  --apl-alias-typography-display-large-line-height: var(--apl-base-typography-line-height-line-height50);
  --apl-alias-typography-display-medium-font-family: var(--apl-base-typography-font-family-ibm-plex-sans-thai);
  --apl-alias-typography-display-medium-font-weight: var(--apl-base-typography-font-weight-weight3);
  --apl-alias-typography-display-medium-font-size: var(--apl-base-typography-font-size-2xl);
  --apl-alias-typography-display-medium-line-height: var(--apl-base-typography-line-height-line-height50);
  --apl-alias-typography-display-small-font-family: var(--apl-base-typography-font-family-ibm-plex-sans-thai);
  --apl-alias-typography-display-small-font-weight: var(--apl-base-typography-font-weight-weight3);
  --apl-alias-typography-display-small-font-size: var(--apl-base-typography-font-size-xl);
  --apl-alias-typography-display-small-line-height: var(--apl-base-typography-line-height-line-height49);
  --apl-alias-typography-headline-large-font-family: var(--apl-base-typography-font-family-ibm-plex-sans-thai);
  --apl-alias-typography-headline-large-font-weight: var(--apl-base-typography-font-weight-weight3);
  --apl-alias-typography-headline-large-font-size: var(--apl-base-typography-font-size-lg);
  --apl-alias-typography-headline-large-line-height: var(--apl-base-typography-line-height-line-height49);
  --apl-alias-typography-headline-medium-font-family: var(--apl-base-typography-font-family-ibm-plex-sans-thai);
  --apl-alias-typography-headline-medium-font-weight: var(--apl-base-typography-font-weight-weight3);
  --apl-alias-typography-headline-medium-font-size: var(--apl-base-typography-font-size-md);
  --apl-alias-typography-headline-medium-line-height: var(--apl-base-typography-line-height-line-height43);
  --apl-alias-typography-headline-small-font-family: var(--apl-base-typography-font-family-ibm-plex-sans-thai);
  --apl-alias-typography-headline-small-font-weight: var(--apl-base-typography-font-weight-weight3);
  --apl-alias-typography-headline-small-font-size: var(--apl-base-typography-font-size-sm);
  --apl-alias-typography-headline-small-line-height: var(--apl-base-typography-line-height-line-height43);
  --apl-alias-typography-title-large-font-family: var(--apl-base-typography-font-family-ibm-plex-sans-thai);
  --apl-alias-typography-title-large-font-weight: var(--apl-base-typography-font-weight-weight2);
  --apl-alias-typography-title-large-font-size: var(--apl-base-typography-font-size-xs-plus);
  --apl-alias-typography-title-large-line-height: var(--apl-base-typography-line-height-line-height43);
  --apl-alias-typography-title-medium-font-family: var(--apl-base-typography-font-family-ibm-plex-sans-thai);
  --apl-alias-typography-title-medium-font-weight: var(--apl-base-typography-font-weight-weight2);
  --apl-alias-typography-title-medium-font-size: var(--apl-base-typography-font-size-xs-md);
  --apl-alias-typography-title-medium-line-height: var(--apl-base-typography-line-height-line-height31);
  --apl-alias-typography-title-small-font-family: var(--apl-base-typography-font-family-ibm-plex-sans-thai);
  --apl-alias-typography-title-small-font-weight: var(--apl-base-typography-font-weight-weight3);
  --apl-alias-typography-title-small-font-size: var(--apl-base-typography-font-size-xs-sm);
  --apl-alias-typography-title-small-line-height: var(--apl-base-typography-line-height-line-height29);
  --apl-alias-typography-body-large-font-family: var(--apl-base-typography-font-family-ibm-plex-sans-thai);
  --apl-alias-typography-body-large-font-weight: var(--apl-base-typography-font-weight-weight1);
  --apl-alias-typography-body-large-weight-emphasized: var(--apl-base-typography-font-weight-weight2);
  --apl-alias-typography-body-large-font-size: var(--apl-base-typography-font-size-xs);
  --apl-alias-typography-body-large-line-height: var(--apl-base-typography-line-height-line-height25);
  --apl-alias-typography-body-medium-font-family: var(--apl-base-typography-font-family-ibm-plex-sans-thai);
  --apl-alias-typography-body-medium-font-weight: var(--apl-base-typography-font-weight-weight1);
  --apl-alias-typography-body-medium-weight-emphasized: var(--apl-base-typography-font-weight-weight2);
  --apl-alias-typography-body-medium-font-size: var(--apl-base-typography-font-size-2xs);
  --apl-alias-typography-body-medium-line-height: var(--apl-base-typography-line-height-line-height21);
  --apl-alias-typography-body-small-font-family: var(--apl-base-typography-font-family-ibm-plex-sans-thai);
  --apl-alias-typography-body-small-font-weight: var(--apl-base-typography-font-weight-weight1);
  --apl-alias-typography-body-small-weight-emphasized: var(--apl-base-typography-font-weight-weight2);
  --apl-alias-typography-body-small-font-size: var(--apl-base-typography-font-size-3xs);
  --apl-alias-typography-body-small-line-height: var(--apl-base-typography-line-height-line-height21);
  --apl-alias-typography-label-large-font-family: var(--apl-base-typography-font-family-ibm-plex-sans-thai);
  --apl-alias-typography-label-large-font-weight: var(--apl-base-typography-font-weight-weight1);
  --apl-alias-typography-label-large-weight-emphasized: var(--apl-base-typography-font-weight-weight2);
  --apl-alias-typography-label-large-font-size: var(--apl-base-typography-font-size-2xs);
  --apl-alias-typography-label-large-line-height: var(--apl-base-typography-line-height-line-height21);
  --apl-alias-typography-label-medium-font-family: var(--apl-base-typography-font-family-ibm-plex-sans-thai);
  --apl-alias-typography-label-medium-font-weight: var(--apl-base-typography-font-weight-weight1);
  --apl-alias-typography-label-medium-weight-emphasized: var(--apl-base-typography-font-weight-weight2);
  --apl-alias-typography-label-medium-font-size: var(--apl-base-typography-font-size-3xs);
  --apl-alias-typography-label-medium-line-height: var(--apl-base-typography-line-height-line-height21);
  --apl-alias-typography-label-small-font-family: var(--apl-base-typography-font-family-ibm-plex-sans-thai);
  --apl-alias-typography-label-small-font-weight: var(--apl-base-typography-font-weight-weight1);
  --apl-alias-typography-label-small-weight-emphasized: var(--apl-base-typography-font-weight-weight2);
  --apl-alias-typography-label-small-font-size: var(--apl-base-typography-font-size-4xs);
  --apl-alias-typography-label-small-line-height: var(--apl-base-typography-line-height-line-height13);
}