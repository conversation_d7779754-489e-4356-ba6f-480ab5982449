import { render, screen } from '@testing-library/react'
import '@testing-library/jest-dom'
import { W3CThemeProvider, useW3CTheme, useW3CThemeTokens } from './W3CThemeProvider'

// Mock fetch for token loading
global.fetch = jest.fn()

const mockTokenResponse = {
  global: {
    color: {
      'green-pine': {
        '50': {
          $type: 'color',
          $value: { hex: '#f0fdf4' }
        }
      }
    }
  }
}

beforeEach(() => {
  ;(fetch as jest.Mock).mockResolvedValue({
    ok: true,
    json: async () => mockTokenResponse
  })
})

afterEach(() => {
  jest.clearAllMocks()
})

// Test component that uses theme hooks
function TestComponent() {
  const theme = useW3CTheme()
  const tokens = useW3CThemeTokens()
  
  return (
    <div>
      <div data-testid="theme-mode">{theme?.mode}</div>
      <div data-testid="theme-device">{theme?.device}</div>
      <div data-testid="theme-typography">{theme?.typography}</div>
      <div data-testid="theme-depth">{theme?.depth}</div>
      <div data-testid="tokens-count">{Object.keys(tokens).length}</div>
    </div>
  )
}

describe('W3CThemeProvider', () => {
  it('provides theme context with default values', async () => {
    render(
      <W3CThemeProvider>
        <TestComponent />
      </W3CThemeProvider>
    )
    
    // Wait for tokens to load
    await screen.findByTestId('theme-mode')
    
    expect(screen.getByTestId('theme-mode')).toHaveTextContent('light')
    expect(screen.getByTestId('theme-device')).toHaveTextContent('desktop')
    expect(screen.getByTestId('theme-typography')).toHaveTextContent('baseline-16px')
    expect(screen.getByTestId('theme-depth')).toHaveTextContent('0')
  })
  
  it('provides theme context with custom values', async () => {
    render(
      <W3CThemeProvider mode="dark" device="mobile" typography="baseline-28px">
        <TestComponent />
      </W3CThemeProvider>
    )
    
    await screen.findByTestId('theme-mode')
    
    expect(screen.getByTestId('theme-mode')).toHaveTextContent('dark')
    expect(screen.getByTestId('theme-device')).toHaveTextContent('mobile')
    expect(screen.getByTestId('theme-typography')).toHaveTextContent('baseline-28px')
  })
  
  it('supports nested themes with inheritance', async () => {
    function NestedTestComponent() {
      const theme = useW3CTheme()
      return <div data-testid="nested-depth">{theme?.depth}</div>
    }
    
    render(
      <W3CThemeProvider mode="light">
        <TestComponent />
        <W3CThemeProvider mode="dark" inheritParentTheme={true}>
          <NestedTestComponent />
        </W3CThemeProvider>
      </W3CThemeProvider>
    )
    
    await screen.findByTestId('theme-mode')
    
    expect(screen.getByTestId('theme-depth')).toHaveTextContent('0')
    expect(screen.getByTestId('nested-depth')).toHaveTextContent('1')
  })
  
  it('loads tokens from multiple files', async () => {
    render(
      <W3CThemeProvider>
        <TestComponent />
      </W3CThemeProvider>
    )
    
    await screen.findByTestId('tokens-count')
    
    // Should have called fetch for multiple token files
    expect(fetch).toHaveBeenCalledWith('/apollo-token/build/tokens/global/color.json')
    expect(fetch).toHaveBeenCalledWith('/apollo-token/build/tokens/system/base/color.json')
    expect(fetch).toHaveBeenCalledWith('/apollo-token/build/tokens/system/alias/color:light-mode.json')
    expect(fetch).toHaveBeenCalledWith('/apollo-token/build/tokens/system/alias/spacing:desktop.json')
    expect(fetch).toHaveBeenCalledWith('/apollo-token/build/tokens/system/alias/typography:baseline-16px.json')
  })
  
  it('applies custom tokens', async () => {
    const customTokens = {
      '--apl-custom-color': '#ff0000'
    }
    
    function CustomTokenTest() {
      const tokens = useW3CThemeTokens()
      return <div data-testid="custom-token">{tokens['--apl-custom-color']}</div>
    }
    
    render(
      <W3CThemeProvider customTokens={customTokens}>
        <CustomTokenTest />
      </W3CThemeProvider>
    )
    
    await screen.findByTestId('custom-token')
    
    expect(screen.getByTestId('custom-token')).toHaveTextContent('#ff0000')
  })
  
  it('handles loading state', () => {
    // Mock fetch to never resolve to test loading state
    ;(fetch as jest.Mock).mockImplementation(() => new Promise(() => {}))
    
    render(
      <W3CThemeProvider>
        <TestComponent />
      </W3CThemeProvider>
    )
    
    expect(screen.getByText('Loading theme...')).toBeInTheDocument()
  })
  
  it('handles fetch errors gracefully', async () => {
    ;(fetch as jest.Mock).mockRejectedValue(new Error('Network error'))
    
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation()
    
    render(
      <W3CThemeProvider>
        <TestComponent />
      </W3CThemeProvider>
    )
    
    // Should still render after error
    await screen.findByTestId('theme-mode')
    
    expect(consoleSpy).toHaveBeenCalledWith('Error loading W3C tokens:', expect.any(Error))
    
    consoleSpy.mockRestore()
  })
})
