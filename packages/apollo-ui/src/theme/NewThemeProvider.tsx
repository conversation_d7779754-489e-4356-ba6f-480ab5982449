import { HTMLElementType, useMemo, create<PERSON>ontext, useContext, type ComponentType, type ReactNode } from "react"

// Token imports from build folder
import globalColor from "../../../apollo-token/build/tokens/global/color.json"
import globalElevation from "../../../apollo-token/build/tokens/global/elevation.json"
import globalRadius from "../../../apollo-token/build/tokens/global/radius.json"
import globalSpacing from "../../../apollo-token/build/tokens/global/spacing.json"
import globalTypography from "../../../apollo-token/build/tokens/global/typography.json"

import systemBaseColor from "../../../apollo-token/build/tokens/system/base/color.json"
import systemBaseElevation from "../../../apollo-token/build/tokens/system/base/elevation.json"
import systemBaseRadius from "../../../apollo-token/build/tokens/system/base/radius.json"
import systemBaseSpacing from "../../../apollo-token/build/tokens/system/base/spacing.json"
import systemBaseTypography16 from "../../../apollo-token/build/tokens/system/base/typography:baseline-16px.json"
import systemBaseTypography28 from "../../../apollo-token/build/tokens/system/base/typography:baseline-28px.json"

import systemAliasColorLight from "../../../apollo-token/build/tokens/system/alias/color:light-mode.json"
import systemAliasColorDark from "../../../apollo-token/build/tokens/system/alias/color:dark-mode.json"
import systemAliasElevation from "../../../apollo-token/build/tokens/system/alias/elevation.json"
import systemAliasRadius from "../../../apollo-token/build/tokens/system/alias/radius.json"
import systemAliasSpacingDesktop from "../../../apollo-token/build/tokens/system/alias/spacing:desktop.json"
import systemAliasSpacingHandheld from "../../../apollo-token/build/tokens/system/alias/spacing:handheld.json"
import systemAliasSpacingMobile from "../../../apollo-token/build/tokens/system/alias/spacing:mobile.json"
import systemAliasTypography16 from "../../../apollo-token/build/tokens/system/alias/typography:baseline-16px.json"
import systemAliasTypography28 from "../../../apollo-token/build/tokens/system/alias/typography:baseline-28px.json"

import { animationStyles } from "./override/animation"
import { baseline } from "./override/baseline"
import { reactDatePickerStyleOverride } from "./override/react-datepicker"
import { zIndexStyles } from "./override/zIndex"

// Theme context for nested theme support
interface ThemeContextValue {
  tokens: Record<string, string>
  mode: 'light' | 'dark'
  device: 'mobile' | 'handheld' | 'desktop'
  typography: '16px' | '28px'
  depth: number
  parentScope?: string
}

const ThemeContext = createContext<ThemeContextValue | null>(null)

export interface NewThemeProviderProps {
  children: ReactNode
  mode?: 'light' | 'dark'
  scope?: string
  customTokens?: Record<string, string>
  device?: 'mobile' | 'handheld' | 'desktop'
  typography?: '16px' | '28px'
  WrapperComponent?: HTMLElementType | ComponentType
  className?: string
  enablePortalTheming?: boolean
  inheritParentTheme?: boolean
  themeId?: string
}

/**
 * Converts W3C token format to CSS variables
 */
function convertTokensToCSSVariables(tokens: any, prefix = "apl"): Record<string, string> {
  const cssVars: Record<string, string> = {}
  
  function traverse(obj: any, path: string[] = []) {
    for (const [key, value] of Object.entries(obj)) {
      if (key.startsWith('$')) {
        // Skip metadata properties
        continue
      }
      
      const currentPath = [...path, key]
      
      if (value && typeof value === 'object' && '$value' in value) {
        // This is a token with a value
        const varName = `--${prefix}-${currentPath.join('-')}`
        const tokenValue = value.$value

        if (tokenValue && typeof tokenValue === 'object' && 'hex' in tokenValue) {
          // Color token with hex value
          cssVars[varName] = (tokenValue as any).hex
        } else if (typeof tokenValue === 'string' && tokenValue.startsWith('{') && tokenValue.endsWith('}')) {
          // Reference to another token
          const refPath = tokenValue.slice(1, -1).split('.')
          const refVarName = `--${prefix}-${refPath.join('-')}`
          cssVars[varName] = `var(${refVarName})`
        } else {
          // Direct value
          cssVars[varName] = String(tokenValue)
        }
      } else if (value && typeof value === 'object') {
        // Nested object, continue traversing
        traverse(value, currentPath)
      }
    }
  }
  
  traverse(tokens)
  return cssVars
}

/**
 * Convert CSS variables object to CSS string
 */
function cssVarsToString(cssVars: Record<string, string>): string {
  return Object.entries(cssVars)
    .map(([key, value]) => `${key}: ${value};`)
    .join(' ')
}

/**
 * Generate unique ID for CSS injection
 */
function generateId(scope?: string): string {
  const base = scope ? scope.replace(/[^a-zA-Z0-9]/g, '-') : 'root'
  return `apl-${base}-${Math.random().toString(36).substring(2, 11)}`
}

export function NewThemeProvider({
  children,
  mode = 'light',
  scope,
  customTokens = {},
  device = 'desktop',
  typography = '16px',
  WrapperComponent,
  className,
  enablePortalTheming = true,
  inheritParentTheme = true,
  themeId,
  ...wrapperProps
}: NewThemeProviderProps) {

  // Get parent theme context
  const parentTheme = useContext(ThemeContext)

  // Determine effective values (inherit from parent if specified)
  const effectiveMode = inheritParentTheme && parentTheme ? parentTheme.mode : mode
  const effectiveDevice = inheritParentTheme && parentTheme ? parentTheme.device : device
  const effectiveTypography = inheritParentTheme && parentTheme ? parentTheme.typography : typography
  const themeDepth = parentTheme ? parentTheme.depth + 1 : 0

  // Load and convert global tokens (always in :root, only for root theme)
  const globalTokens = useMemo(() => {
    if (parentTheme && inheritParentTheme) {
      // Nested themes don't inject global tokens
      return {}
    }
    const merged = {
      ...globalColor,
      ...globalElevation,
      ...globalRadius,
      ...globalSpacing,
      ...globalTypography,
    }
    return convertTokensToCSSVariables(merged)
  }, [parentTheme, inheritParentTheme])

  // Load and convert system tokens (default brand)
  const systemTokens = useMemo(() => {
    // Base tokens
    const baseTokens = {
      ...systemBaseColor,
      ...systemBaseElevation,
      ...systemBaseRadius,
      ...systemBaseSpacing,
      ...(effectiveTypography === '16px' ? systemBaseTypography16 : systemBaseTypography28),
    }

    // Alias tokens based on mode and device
    const colorAlias = effectiveMode === 'dark' ? systemAliasColorDark : systemAliasColorLight
    let spacingAlias
    if (effectiveDevice === 'mobile') {
      spacingAlias = systemAliasSpacingMobile
    } else if (effectiveDevice === 'handheld') {
      spacingAlias = systemAliasSpacingHandheld
    } else {
      spacingAlias = systemAliasSpacingDesktop
    }
    const typographyAlias = effectiveTypography === '16px' ? systemAliasTypography16 : systemAliasTypography28

    const aliasTokens = {
      ...colorAlias,
      ...systemAliasElevation,
      ...systemAliasRadius,
      ...spacingAlias,
      ...typographyAlias,
    }

    const merged = { ...baseTokens, ...aliasTokens }
    return convertTokensToCSSVariables(merged)
  }, [effectiveMode, effectiveDevice, effectiveTypography])

  // Merge with custom tokens and parent theme
  const finalTokens = useMemo(() => {
    let baseTokens = systemTokens

    // If inheriting from parent, merge parent tokens first
    if (inheritParentTheme && parentTheme) {
      baseTokens = { ...parentTheme.tokens, ...systemTokens }
    }

    return { ...baseTokens, ...customTokens }
  }, [systemTokens, customTokens, inheritParentTheme, parentTheme])

  // Generate CSS strings
  const globalCSS = useMemo(() => cssVarsToString(globalTokens), [globalTokens])
  const systemCSS = useMemo(() => cssVarsToString(finalTokens), [finalTokens])

  // Style overrides
  const overrideStyles = [
    reactDatePickerStyleOverride,
    animationStyles,
    zIndexStyles,
    baseline,
  ]

  // Generate IDs and selectors
  const globalId = "apl-global-tokens"
  const systemId = generateId(themeId || scope)
  const isNestedTheme = Boolean(parentTheme)

  // Generate CSS selector based on nesting
  let cssSelector: string
  if (scope) {
    cssSelector = scope
  } else if (isNestedTheme) {
    // Nested theme without explicit scope gets a generated selector
    cssSelector = `[data-theme-id="${systemId}"]`
  } else {
    // Root theme
    cssSelector = ":root"
  }

  // Determine wrapper component
  let Wrapper: HTMLElementType | ComponentType = "div"
  if (WrapperComponent) {
    Wrapper = WrapperComponent
  }

  const hasWrapper = Boolean(scope) || isNestedTheme

  // Create theme context value
  const themeContextValue: ThemeContextValue = {
    tokens: finalTokens,
    mode: effectiveMode,
    device: effectiveDevice,
    typography: effectiveTypography,
    depth: themeDepth,
    parentScope: parentTheme?.parentScope || scope,
  }

  // Generate portal-specific CSS for modals and overlays
  const portalCSS = useMemo(() => {
    return `
      /* Portal containers inherit theme variables */
      [data-portal="true"],
      [data-modal="true"],
      [data-overlay="true"],
      [data-tooltip="true"],
      [data-popover="true"],
      [data-dropdown="true"],
      .modal-portal,
      .overlay-portal,
      .tooltip-portal,
      .popover-portal,
      .dropdown-portal,
      .dialog-portal,
      #modal-root,
      #overlay-root,
      #portal-root,
      #tooltip-root,
      #popover-root,
      #dropdown-root,
      #dialog-root,
      body > div[class*="modal"],
      body > div[class*="overlay"],
      body > div[class*="portal"],
      body > div[class*="tooltip"],
      body > div[class*="popover"],
      body > div[class*="dropdown"],
      body > div[class*="dialog"],
      body > div[id*="modal"],
      body > div[id*="overlay"],
      body > div[id*="portal"],
      body > div[id*="tooltip"],
      body > div[id*="popover"],
      body > div[id*="dropdown"],
      body > div[id*="dialog"],
      div[role="dialog"],
      div[role="tooltip"],
      div[role="menu"],
      div[role="listbox"],
      div[role="combobox"],
      .react-modal,
      .ReactModal__Content,
      .modal,
      .overlay,
      .tooltip,
      .popover,
      .dropdown,
      .dialog {
        ${systemCSS}
      }

      /* Ensure all children in portals inherit theme variables */
      [data-portal="true"] *,
      [data-modal="true"] *,
      [data-overlay="true"] *,
      [data-tooltip="true"] *,
      [data-popover="true"] *,
      [data-dropdown="true"] *,
      .modal-portal *,
      .overlay-portal *,
      .tooltip-portal *,
      .popover-portal *,
      .dropdown-portal *,
      .dialog-portal *,
      .react-modal *,
      .ReactModal__Content *,
      .modal *,
      .overlay *,
      .tooltip *,
      .popover *,
      .dropdown *,
      .dialog *,
      div[role="dialog"] *,
      div[role="tooltip"] *,
      div[role="menu"] *,
      div[role="listbox"] *,
      div[role="combobox"] * {
        /* CSS variables are inherited automatically */
      }

      /* Specific targeting for common modal libraries */
      .ReactModalPortal,
      .ReactModalPortal *,
      [data-reach-dialog-overlay],
      [data-reach-dialog-overlay] *,
      [data-reach-dialog-content],
      [data-reach-dialog-content] *,
      .chakra-modal__overlay,
      .chakra-modal__overlay *,
      .chakra-modal__content,
      .chakra-modal__content *,
      .ant-modal-root,
      .ant-modal-root *,
      .ant-modal-wrap,
      .ant-modal-wrap *,
      .MuiModal-root,
      .MuiModal-root *,
      .MuiDialog-root,
      .MuiDialog-root *,
      .mantine-Modal-root,
      .mantine-Modal-root *,
      .bp3-overlay,
      .bp3-overlay *,
      .bp3-dialog,
      .bp3-dialog * {
        ${systemCSS}
      }
    `
  }, [systemCSS])

  return (
    <ThemeContext.Provider value={themeContextValue}>
      {/* Only inject global styles for root theme */}
      {!isNestedTheme && (
        <>
          {/* Global tokens always in :root */}
          <style
            dangerouslySetInnerHTML={{
              __html: `:root { ${globalCSS} }`,
            }}
            data-apl={globalId}
          />

          {/* Ensure body also has access to system tokens for portals */}
          <style
            dangerouslySetInnerHTML={{
              __html: `body { ${systemCSS} }`,
            }}
            data-apl={`${systemId}-body`}
          />

          {/* Font imports */}
          <style
            dangerouslySetInnerHTML={{
              __html: "@import url('https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Thai:wght@100;200;300;400;500;600;700&display=swap');",
            }}
            data-apl={`${systemId}-fonts`}
          />

          {/* Style overrides */}
          <style
            dangerouslySetInnerHTML={{
              __html: overrideStyles.join(""),
            }}
            data-apl={`${systemId}-overrides`}
          />
        </>
      )}

      {/* System tokens for this theme level */}
      <style
        dangerouslySetInnerHTML={{
          __html: `${cssSelector} { ${systemCSS} }`,
        }}
        data-apl={`${systemId}-system`}
      />

      {/* Portal and modal styling (only for root theme) */}
      {enablePortalTheming && !isNestedTheme && (
        <style
          dangerouslySetInnerHTML={{
            __html: portalCSS,
          }}
          data-apl={`${systemId}-portals`}
        />
      )}

      {hasWrapper ? (
        <Wrapper
          className={className}
          data-theme-id={isNestedTheme && !scope ? systemId : undefined}
          data-theme-depth={themeDepth}
          {...wrapperProps}
        >
          {children}
        </Wrapper>
      ) : (
        children
      )}
    </ThemeContext.Provider>
  )
}

/**
 * Hook to access the current theme context
 */
export function useTheme(): ThemeContextValue | null {
  return useContext(ThemeContext)
}

/**
 * Hook to get theme tokens from the current context
 */
export function useThemeTokens(): Record<string, string> {
  const theme = useContext(ThemeContext)
  return theme?.tokens || {}
}

/**
 * Utility function to create portal containers with proper theme attributes
 */
export function createThemedPortal(containerId: string = 'portal-root'): HTMLElement {
  let container = document.getElementById(containerId)

  if (!container) {
    container = document.createElement('div')
    container.id = containerId
    container.setAttribute('data-portal', 'true')
    document.body.appendChild(container)
  }

  return container
}

/**
 * Utility function to add theme attributes to existing portal containers
 */
export function enablePortalTheming(element: HTMLElement): void {
  element.setAttribute('data-portal', 'true')
}

/**
 * Utility function to apply theme styles directly to any element
 * This is useful for portals that don't get caught by CSS selectors
 */
export function applyThemeToElement(element: HTMLElement, tokens?: Record<string, string>): void {
  // Add portal attribute for CSS targeting
  element.setAttribute('data-portal', 'true')

  // If specific tokens are provided, apply them directly as inline styles
  if (tokens) {
    Object.entries(tokens).forEach(([property, value]) => {
      element.style.setProperty(property, value)
    })
  }
}

/**
 * Utility function to inject theme styles into any container
 * This creates a style element within the target container
 */
export function injectThemeIntoContainer(container: HTMLElement, tokens: Record<string, string>): void {
  const styleId = 'apl-injected-theme'

  // Remove existing style if present
  const existingStyle = container.querySelector(`#${styleId}`)
  if (existingStyle) {
    existingStyle.remove()
  }

  // Create new style element
  const style = document.createElement('style')
  style.id = styleId
  style.setAttribute('data-apl', 'injected-theme')

  // Generate CSS
  const css = Object.entries(tokens)
    .map(([property, value]) => `${property}: ${value};`)
    .join(' ')

  style.textContent = `* { ${css} }`

  // Inject into container
  container.appendChild(style)
}

/**
 * Utility function to create modal containers with proper theme attributes
 */
export function createThemedModal(containerId: string = 'modal-root'): HTMLElement {
  let container = document.getElementById(containerId)

  if (!container) {
    container = document.createElement('div')
    container.id = containerId
    container.setAttribute('data-modal', 'true')
    container.setAttribute('data-portal', 'true')
    document.body.appendChild(container)
  }

  return container
}

export default NewThemeProvider
