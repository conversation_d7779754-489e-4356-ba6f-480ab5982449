import { createContext, useContext, useEffect, useMemo, useState, ReactNode, ComponentType } from 'react'

// Type aliases for better maintainability
type ThemeMode = 'light' | 'dark'
type ThemeDevice = 'mobile' | 'handheld' | 'desktop'
type ThemeTypography = 'baseline-16px' | 'baseline-28px'

// W3C Token interfaces
interface W3CTokenValue {
  $type: string
  $value: any
  $description?: string
}

interface W3CTokenGroup {
  [key: string]: W3CTokenValue | W3CTokenGroup | { $description?: string }
}

interface W3CTokens {
  global?: W3CTokenGroup
  base?: W3CTokenGroup
  alias?: W3CTokenGroup
}

// Theme context for nested theme support
interface W3CThemeContextValue {
  tokens: Record<string, string>
  mode: ThemeMode
  device: ThemeDevice
  typography: ThemeTypography
  depth: number
  parentScope?: string
}

const W3CThemeContext = createContext<W3CThemeContextValue | null>(null)

export interface W3CThemeProviderProps {
  readonly children: ReactNode
  readonly mode?: ThemeMode
  readonly scope?: string
  readonly customTokens?: Record<string, string>
  readonly device?: ThemeDevice
  readonly typography?: ThemeTypography
  readonly WrapperComponent?: keyof JSX.IntrinsicElements | ComponentType
  readonly className?: string
  readonly enablePortalTheming?: boolean
  readonly inheritParentTheme?: boolean
  readonly themeId?: string
}

// Token loading utilities
async function loadTokenFile(path: string): Promise<W3CTokens | null> {
  try {
    const response = await fetch(path)
    if (!response.ok) {
      console.warn(`Failed to load token file: ${path}`)
      return null
    }
    return await response.json()
  } catch (error) {
    console.warn(`Error loading token file ${path}:`, error)
    return null
  }
}







// Token resolution using the same approach as example-usage.js
function resolveTokenReferences(tokens: W3CTokens): Record<string, string> {
  const resolved: Record<string, string> = {}

  // Flatten tokens into a flat structure (same as example-usage.js)
  function flattenTokens(obj: any, prefix = ''): Record<string, W3CTokenValue> {
    const flattened: Record<string, W3CTokenValue> = {}

    for (const [key, value] of Object.entries(obj)) {
      if (key.startsWith('$')) {
        continue // Skip metadata properties
      }

      const currentPath = prefix ? `${prefix}.${key}` : key

      if (value && typeof value === 'object' && '$value' in value) {
        // This is a token
        flattened[currentPath] = value as W3CTokenValue
      } else if (value && typeof value === 'object') {
        // This is a group, recurse
        Object.assign(flattened, flattenTokens(value, currentPath))
      }
    }

    return flattened
  }

  // Convert any token value to CSS (same as example-usage.js)
  function tokenToCSS(token: W3CTokenValue): string {
    const value = token.$value

    // Handle token references
    if (typeof value === 'string' && value.startsWith('{') && value.endsWith('}')) {
      const refPath = value.slice(1, -1).replace(/\./g, '-').toLowerCase()
      return `var(--${refPath})`
    }

    switch (token.$type) {
      case 'color':
        if (typeof value === 'object' && value.hex) {
          return value.alpha !== undefined ?
            `${value.hex}${Math.round(value.alpha * 255).toString(16).padStart(2, '0')}` :
            value.hex
        }
        if (typeof value === 'object' && value.components) {
          const [r, g, b] = value.components.map((c: number) => Math.round(c * 255))
          return value.alpha !== undefined ?
            `rgba(${r}, ${g}, ${b}, ${value.alpha})` :
            `rgb(${r}, ${g}, ${b})`
        }
        break
      case 'dimension':
        if (typeof value === 'object' && value.value !== undefined && value.unit !== undefined) {
          return `${value.value}${value.unit}`
        }
        break
      case 'fontFamily':
        return Array.isArray(value) ?
          value.map((f: string) => `"${f}"`).join(', ') :
          `"${value}"`
      case 'fontWeight':
        return value.toString()
      case 'number':
        return value.toString()
      default:
        return value.toString()
    }

    return value.toString()
  }

  // Flatten all tokens
  const flattened = flattenTokens(tokens)

  // Convert to CSS variables with apl prefix (same as example-usage.js approach)
  for (const [path, token] of Object.entries(flattened)) {
    const cssVar = `--apl-${path.replace(/\./g, '-').replace(/\//g, '-').toLowerCase()}`
    const cssValue = tokenToCSS(token)
    resolved[cssVar] = cssValue
  }

  console.log(`Resolved ${Object.keys(resolved).length} tokens`)
  return resolved
}

// Token loading hook
function useW3CTokens(mode: ThemeMode, device: ThemeDevice, typography: ThemeTypography) {
  const [tokens, setTokens] = useState<Record<string, string>>({})
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    async function loadTokens() {
      setLoading(true)

      try {
        // Load all tokens using the same approach as example-usage.js
        const allTokens: W3CTokens = { global: {}, base: {}, alias: {} }

        // Helper function to deep merge objects (same as example-usage.js)
        function deepMerge(target: any, source: any): any {
          for (const key in source) {
            if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
              if (!target[key]) target[key] = {}
              deepMerge(target[key], source[key])
            } else {
              target[key] = source[key]
            }
          }
          return target
        }

        // Load all global token files
        try {
          const globalResponse = await fetch('/apollo-token/build/tokens/global/')
          if (globalResponse.ok) {
            const globalFiles = ['color.json', 'elevation.json', 'radius.json', 'spacing.json', 'typography.json']
            for (const file of globalFiles) {
              try {
                const tokens = await loadTokenFile(`/apollo-token/build/tokens/global/${file}`)
                if (tokens) {
                  deepMerge(allTokens, tokens)
                }
              } catch (error) {
                console.warn(`Failed to load global/${file}:`, error)
              }
            }
          }
        } catch (error) {
          console.warn('Failed to load global tokens:', error)
        }

        // Load all base token files
        try {
          const baseFiles = ['color.json', 'elevation.json', 'radius.json', 'spacing.json', 'typography:baseline-16px.json', 'typography:baseline-28px.json']
          for (const file of baseFiles) {
            try {
              const tokens = await loadTokenFile(`/apollo-token/build/tokens/system/base/${file}`)
              if (tokens) {
                deepMerge(allTokens, tokens)
              }
            } catch (error) {
              console.warn(`Failed to load base/${file}:`, error)
            }
          }
        } catch (error) {
          console.warn('Failed to load base tokens:', error)
        }

        // Load all alias token files
        try {
          const aliasFiles = [
            'color:dark-mode.json', 'color:light-mode.json', 'elevation.json', 'radius.json',
            'spacing:desktop.json', 'spacing:handheld.json', 'spacing:mobile.json',
            'typography:baseline-16px.json', 'typography:baseline-28px.json'
          ]
          for (const file of aliasFiles) {
            try {
              const tokens = await loadTokenFile(`/apollo-token/build/tokens/system/alias/${file}`)
              if (tokens) {
                deepMerge(allTokens, tokens)
              }
            } catch (error) {
              console.warn(`Failed to load alias/${file}:`, error)
            }
          }
        } catch (error) {
          console.warn('Failed to load alias tokens:', error)
        }

        // Resolve references and convert to CSS variables
        const resolvedTokens = resolveTokenReferences(allTokens)
        setTokens(resolvedTokens)
      } catch (error) {
        console.error('Error loading W3C tokens:', error)
        setTokens({})
      } finally {
        setLoading(false)
      }
    }
    
    loadTokens()
  }, [mode, device, typography])
  
  return { tokens, loading }
}

export function W3CThemeProvider({
  children,
  mode = 'light',
  scope,
  customTokens = {},
  device = 'desktop',
  typography = 'baseline-16px',
  WrapperComponent = 'div',
  className,
  enablePortalTheming = true,
  inheritParentTheme = false,
  themeId,
}: W3CThemeProviderProps) {
  const parentTheme = useContext(W3CThemeContext)
  
  // Determine effective values (inherit from parent if specified)
  const effectiveMode = inheritParentTheme && parentTheme ? parentTheme.mode : mode
  const effectiveDevice = inheritParentTheme && parentTheme ? parentTheme.device : device
  const effectiveTypography = inheritParentTheme && parentTheme ? parentTheme.typography : typography
  const themeDepth = parentTheme ? parentTheme.depth + 1 : 0
  
  // Load W3C tokens
  const { tokens: systemTokens, loading } = useW3CTokens(effectiveMode, effectiveDevice, effectiveTypography)
  
  // Merge with custom tokens and parent theme
  const finalTokens = useMemo(() => {
    let merged = { ...systemTokens }
    
    // Inherit parent tokens if specified
    if (inheritParentTheme && parentTheme) {
      merged = { ...parentTheme.tokens, ...merged }
    }
    
    // Apply custom tokens
    merged = { ...merged, ...customTokens }
    
    return merged
  }, [systemTokens, customTokens, parentTheme, inheritParentTheme])
  
  // Generate CSS variables
  const cssVariables = useMemo(() => {
    return Object.entries(finalTokens)
      .map(([key, value]) => `${key}: ${value};`)
      .join('\n')
  }, [finalTokens])
  
  // Create theme context value
  const themeContextValue: W3CThemeContextValue = useMemo(() => ({
    tokens: finalTokens,
    mode: effectiveMode,
    device: effectiveDevice,
    typography: effectiveTypography,
    depth: themeDepth,
    parentScope: parentTheme?.parentScope || scope,
  }), [finalTokens, effectiveMode, effectiveDevice, effectiveTypography, themeDepth, parentTheme?.parentScope, scope])
  
  // Generate theme scope
  const themeScope = themeId || `w3c-theme-${themeDepth}`
  const scopeSelector = scope || `[data-theme-id="${themeScope}"]`
  
  // Inject CSS variables
  useEffect(() => {
    if (loading || !cssVariables) return
    
    const styleId = `w3c-theme-${themeScope}`
    let styleElement = document.getElementById(styleId) as HTMLStyleElement
    
    if (!styleElement) {
      styleElement = document.createElement('style')
      styleElement.id = styleId
      document.head.appendChild(styleElement)
    }
    
    // Only root theme injects global styles for performance
    const isRootTheme = themeDepth === 0
    const globalStyles = isRootTheme ? `:root {\n${cssVariables}\n}\n` : ''
    const scopedStyles = `${scopeSelector} {\n${cssVariables}\n}`
    
    styleElement.textContent = globalStyles + scopedStyles
    
    return () => {
      styleElement?.parentNode?.removeChild(styleElement)
    }
  }, [cssVariables, scopeSelector, themeScope, themeDepth, loading])
  
  // Portal theming (only for root theme)
  useEffect(() => {
    if (!enablePortalTheming || themeDepth > 0 || loading) return
    
    const portalStyleId = 'w3c-portal-theming'
    let portalStyleElement = document.getElementById(portalStyleId) as HTMLStyleElement
    
    if (!portalStyleElement) {
      portalStyleElement = document.createElement('style')
      portalStyleElement.id = portalStyleId
      document.head.appendChild(portalStyleElement)
    }
    
    // Portal selectors for common modal libraries
    const portalSelectors = [
      '.ReactModal__Content',
      '.chakra-modal__content',
      '.ant-modal',
      '.MuiDialog-root',
      '[data-reach-dialog-content]',
      '[role="dialog"]',
      '[role="alertdialog"]'
    ].join(', ')
    
    portalStyleElement.textContent = `${portalSelectors} {\n${cssVariables}\n}`
    
    return () => {
      portalStyleElement?.parentNode?.removeChild(portalStyleElement)
    }
  }, [cssVariables, enablePortalTheming, themeDepth, loading])
  
  if (loading) {
    return <div>Loading theme...</div>
  }
  
  const themeProps = {
    'data-theme-id': themeScope,
    'data-theme-mode': effectiveMode,
    'data-theme-device': effectiveDevice,
    'data-theme-typography': effectiveTypography,
    'data-theme-depth': themeDepth,
    className,
  }
  
  return (
    <W3CThemeContext.Provider value={themeContextValue}>
      <WrapperComponent {...themeProps}>
        {children}
      </WrapperComponent>
    </W3CThemeContext.Provider>
  )
}

// Theme hooks
export function useW3CTheme(): W3CThemeContextValue | null {
  return useContext(W3CThemeContext)
}

export function useW3CThemeTokens(): Record<string, string> {
  const theme = useContext(W3CThemeContext)
  return theme?.tokens || {}
}
