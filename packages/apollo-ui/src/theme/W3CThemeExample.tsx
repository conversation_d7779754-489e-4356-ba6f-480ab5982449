import React, { useState } from 'react'
import { W3CThemeProvider, useW3CTheme, useW3CThemeTokens } from './W3CThemeProvider'

// Component to display theme information
function ThemeInfo() {
  const theme = useW3CTheme()
  const tokens = useW3CThemeTokens()
  
  if (!theme) return <div>No theme context</div>
  
  return (
    <div style={{ 
      padding: '12px', 
      margin: '8px 0',
      border: '1px solid var(--apl-alias-color-outline-outline, #ccc)',
      borderRadius: '8px',
      backgroundColor: 'var(--apl-alias-color-surface-surface, #fff)',
      color: 'var(--apl-alias-color-on-surface-on-surface, #000)'
    }}>
      <h4 style={{ margin: '0 0 8px 0', color: 'var(--apl-alias-color-primary-primary, #2563eb)' }}>
        Theme Info
      </h4>
      Depth: {theme.depth}<br />
      Mode: {theme.mode}<br />
      Device: {theme.device}<br />
      Typography: {theme.typography}<br />
      Tokens: {Object.keys(tokens).length} loaded
    </div>
  )
}

// Sample component using theme tokens
function ThemedCard({ title, children }: { title: string; children: React.ReactNode }) {
  return (
    <div style={{
      padding: '16px',
      margin: '16px 0',
      borderRadius: 'var(--apl-global-radius-md, 8px)',
      backgroundColor: 'var(--apl-alias-color-surface-surface-container, #f5f5f5)',
      border: '1px solid var(--apl-alias-color-outline-outline, #e0e0e0)',
      boxShadow: 'var(--apl-global-elevation-level1, 0 1px 3px rgba(0,0,0,0.1))'
    }}>
      <h3 style={{ 
        margin: '0 0 12px 0',
        color: 'var(--apl-alias-color-on-surface-on-surface, #1a1a1a)',
        fontSize: 'var(--apl-alias-typography-headline-small-font-size, 1.25rem)'
      }}>
        {title}
      </h3>
      <div style={{ color: 'var(--apl-alias-color-on-surface-on-surface-variant, #666)' }}>
        {children}
      </div>
    </div>
  )
}

// Button component using theme tokens
function ThemedButton({ children, variant = 'primary', onClick }: { 
  children: React.ReactNode
  variant?: 'primary' | 'secondary'
  onClick?: () => void 
}) {
  const isPrimary = variant === 'primary'
  
  return (
    <button
      onClick={onClick}
      style={{
        padding: 'var(--apl-alias-spacing-padding-md, 12px) var(--apl-alias-spacing-padding-lg, 16px)',
        borderRadius: 'var(--apl-global-radius-sm, 6px)',
        border: 'none',
        cursor: 'pointer',
        fontSize: 'var(--apl-alias-typography-body-medium-font-size, 1rem)',
        fontWeight: 'var(--apl-alias-typography-body-medium-font-weight, 500)',
        backgroundColor: isPrimary 
          ? 'var(--apl-alias-color-primary-primary, #2563eb)' 
          : 'var(--apl-alias-color-surface-surface-variant, #f0f0f0)',
        color: isPrimary 
          ? 'var(--apl-alias-color-primary-on-primary, #fff)' 
          : 'var(--apl-alias-color-on-surface-on-surface, #1a1a1a)',
        transition: 'all 0.2s ease',
      }}
      onMouseOver={(e) => {
        e.currentTarget.style.opacity = '0.9'
        e.currentTarget.style.transform = 'translateY(-1px)'
      }}
      onMouseOut={(e) => {
        e.currentTarget.style.opacity = '1'
        e.currentTarget.style.transform = 'translateY(0)'
      }}
    >
      {children}
    </button>
  )
}

// Main example component
export const W3CThemeExample = () => {
  const [mode, setMode] = useState<'light' | 'dark'>('light')
  const [device, setDevice] = useState<'mobile' | 'handheld' | 'desktop'>('desktop')
  const [typography, setTypography] = useState<'baseline-16px' | 'baseline-28px'>('baseline-16px')
  const [showNested, setShowNested] = useState(false)

  return (
    <div style={{ padding: '20px', fontFamily: 'system-ui, sans-serif' }}>
      <h1>W3C Theme Provider Example</h1>
      <p>This example demonstrates the W3C ThemeProvider loading tokens directly from apollo-token build files.</p>

      {/* Theme Controls */}
      <div style={{ 
        padding: '16px', 
        marginBottom: '20px',
        backgroundColor: '#f8f9fa',
        borderRadius: '8px',
        border: '1px solid #e9ecef'
      }}>
        <h3 style={{ marginTop: 0 }}>Theme Configuration</h3>
        
        <div style={{ display: 'flex', gap: '16px', flexWrap: 'wrap', alignItems: 'center' }}>
          <label>
            Mode:
            <select 
              value={mode} 
              onChange={(e) => setMode(e.target.value as 'light' | 'dark')}
              style={{ marginLeft: '8px', padding: '4px' }}
            >
              <option value="light">Light</option>
              <option value="dark">Dark</option>
            </select>
          </label>

          <label>
            Device:
            <select 
              value={device} 
              onChange={(e) => setDevice(e.target.value as 'mobile' | 'handheld' | 'desktop')}
              style={{ marginLeft: '8px', padding: '4px' }}
            >
              <option value="mobile">Mobile</option>
              <option value="handheld">Handheld</option>
              <option value="desktop">Desktop</option>
            </select>
          </label>

          <label>
            Typography:
            <select 
              value={typography} 
              onChange={(e) => setTypography(e.target.value as 'baseline-16px' | 'baseline-28px')}
              style={{ marginLeft: '8px', padding: '4px' }}
            >
              <option value="baseline-16px">16px Baseline</option>
              <option value="baseline-28px">28px Baseline</option>
            </select>
          </label>

          <label>
            <input
              type="checkbox"
              checked={showNested}
              onChange={(e) => setShowNested(e.target.checked)}
            />
            {' '}Show Nested Themes
          </label>
        </div>
      </div>

      {/* Root Theme */}
      <W3CThemeProvider 
        mode={mode} 
        device={device} 
        typography={typography}
        enablePortalTheming={true}
      >
        <div style={{ 
          border: '3px solid #2563eb', 
          padding: '16px', 
          borderRadius: '12px',
          backgroundColor: 'var(--apl-alias-color-surface-surface, #fff)'
        }}>
          <h2 style={{ color: '#2563eb', marginTop: 0 }}>Root W3C Theme (Blue Border)</h2>
          
          <ThemeInfo />
          
          <ThemedCard title="Sample Content">
            <p>This content uses W3C design tokens loaded directly from apollo-token build files.</p>
            <p>The tokens are automatically resolved with proper inheritance and reference resolution.</p>
            
            <div style={{ marginTop: '16px', display: 'flex', gap: '12px', flexWrap: 'wrap' }}>
              <ThemedButton variant="primary">Primary Action</ThemedButton>
              <ThemedButton variant="secondary">Secondary Action</ThemedButton>
            </div>
          </ThemedCard>

          {showNested && (
            <W3CThemeProvider 
              mode={mode === 'light' ? 'dark' : 'light'}
              customTokens={{
                '--apl-alias-color-primary-primary': '#dc2626',
                '--apl-alias-color-primary-on-primary': '#ffffff'
              }}
              inheritParentTheme={true}
              themeId="nested-red-theme"
            >
              <div style={{ 
                border: '3px solid #dc2626', 
                padding: '16px', 
                borderRadius: '12px',
                margin: '16px 0',
                backgroundColor: 'var(--apl-alias-color-surface-surface, #fff)'
              }}>
                <h2 style={{ color: '#dc2626', marginTop: 0 }}>Nested Theme (Red Border)</h2>
                <p style={{ fontSize: '14px', color: '#666', marginBottom: '16px' }}>
                  This nested theme inherits parent settings but overrides the primary color and inverts the mode.
                </p>
                
                <ThemeInfo />
                
                <ThemedCard title="Nested Content">
                  <p>This content demonstrates nested theme inheritance with W3C tokens.</p>
                  <p>Notice how the mode is inverted and primary color is overridden to red.</p>
                  
                  <div style={{ marginTop: '16px', display: 'flex', gap: '12px', flexWrap: 'wrap' }}>
                    <ThemedButton variant="primary">Red Primary</ThemedButton>
                    <ThemedButton variant="secondary">Inherited Secondary</ThemedButton>
                  </div>
                </ThemedCard>

                <W3CThemeProvider 
                  mode="light"
                  device="mobile"
                  typography="baseline-28px"
                  inheritParentTheme={false}
                  customTokens={{
                    '--apl-alias-color-primary-primary': '#059669',
                    '--apl-alias-color-primary-on-primary': '#ffffff'
                  }}
                  themeId="independent-green-theme"
                >
                  <div style={{ 
                    border: '3px solid #059669', 
                    padding: '16px', 
                    borderRadius: '12px',
                    margin: '16px 0',
                    backgroundColor: 'var(--apl-alias-color-surface-surface, #fff)'
                  }}>
                    <h2 style={{ color: '#059669', marginTop: 0 }}>Independent Theme (Green Border)</h2>
                    <p style={{ fontSize: '14px', color: '#666', marginBottom: '16px' }}>
                      This theme does NOT inherit from parent. It uses its own light mode, mobile device, and 28px typography.
                    </p>
                    
                    <ThemeInfo />
                    
                    <ThemedCard title="Independent Content">
                      <p>This demonstrates a completely independent nested theme with W3C tokens.</p>
                      <p>It has its own configuration and green primary color.</p>
                      
                      <div style={{ marginTop: '16px', display: 'flex', gap: '12px', flexWrap: 'wrap' }}>
                        <ThemedButton variant="primary">Green Primary</ThemedButton>
                        <ThemedButton variant="secondary">Independent Secondary</ThemedButton>
                      </div>
                    </ThemedCard>
                  </div>
                </W3CThemeProvider>
              </div>
            </W3CThemeProvider>
          )}

          <div style={{ marginTop: '24px', padding: '16px', backgroundColor: '#f8f9fa', borderRadius: '8px' }}>
            <h4 style={{ marginTop: 0 }}>Current Configuration:</h4>
            <ul style={{ margin: '8px 0' }}>
              <li>Mode: {mode}</li>
              <li>Device: {device}</li>
              <li>Typography: {typography}</li>
              <li>Nested Themes: {showNested ? 'Enabled' : 'Disabled'}</li>
            </ul>
          </div>
        </div>
      </W3CThemeProvider>
    </div>
  )
}
