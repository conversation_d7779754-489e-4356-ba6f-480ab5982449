# Apollo Theme Provider

The Apollo Theme Provider loads design tokens from the `packages/apollo-token/build/tokens` folder and provides them as CSS variables to your application.

## Token Structure

The tokens are organized in the following structure:

```
packages/apollo-token/build/tokens/
├── global/           # Global base tokens (always in :root)
│   ├── color.json
│   ├── elevation.json
│   ├── radius.json
│   ├── spacing.json
│   └── typography.json
└── system/           # System tokens (default brand)
    ├── base/         # Base system tokens
    │   ├── color.json
    │   ├── elevation.json
    │   ├── radius.json
    │   ├── spacing.json
    │   ├── typography:baseline-16px.json
    │   └── typography:baseline-28px.json
    └── alias/        # Alias tokens (semantic tokens)
        ├── color:light-mode.json
        ├── color:dark-mode.json
        ├── elevation.json
        ├── radius.json
        ├── spacing:desktop.json
        ├── spacing:handheld.json
        ├── spacing:mobile.json
        ├── typography:baseline-16px.json
        └── typography:baseline-28px.json
```

## Usage

### Basic Usage

```tsx
import { ThemeProvider } from '@apollo/ui'

function App() {
  return (
    <ThemeProvider>
      <div>Your app content</div>
    </ThemeProvider>
  )
}
```

### Dark Mode

```tsx
import { ThemeProvider } from '@apollo/ui'

function App() {
  const [isDark, setIsDark] = useState(false)
  
  return (
    <ThemeProvider darkMode={isDark}>
      <div>Your app content</div>
    </ThemeProvider>
  )
}
```

### Custom Theme Override

```tsx
import { ThemeProvider } from '@apollo/ui'

function App() {
  const customTheme = {
    '--apl-colors-content-primary-default': '#ff0000', // Override primary color
  }
  
  return (
    <ThemeProvider theme={customTheme}>
      <div>Your app content</div>
    </ThemeProvider>
  )
}
```

### Scoped Theme

```tsx
import { ThemeProvider } from '@apollo/ui'

function App() {
  return (
    <div>
      <div>Default theme content</div>
      
      <ThemeProvider scope=".custom-section" theme={customTheme}>
        <div className="custom-section">
          Custom themed content
        </div>
      </ThemeProvider>
    </div>
  )
}
```

## Token Loading

The ThemeProvider automatically loads tokens from the build folder:

- **Global tokens** are always injected into `:root` and contain base design values
- **System tokens** are used as the default brand and contain semantic color mappings
- **Dark mode** uses alternative alias tokens for color schemes

## Responsive and Typography Utilities

```tsx
import { getResponsiveSpacing, getTypographyTokens } from '@apollo/ui/theme'

// Get spacing tokens for different devices
const mobileSpacing = getResponsiveSpacing('mobile')
const desktopSpacing = getResponsiveSpacing('desktop')

// Get typography tokens for different baselines
const typography16 = getTypographyTokens('16px')
const typography28 = getTypographyTokens('28px')
```

## Portal and Modal Support

The ThemeProvider automatically ensures that modals, overlays, and portals have access to design tokens:

### Automatic Portal Detection

The following elements automatically receive theme tokens:
- Elements with `data-portal`, `data-modal`, `data-overlay` attributes
- Common portal containers: `.modal-portal`, `.overlay-portal`, `#modal-root`, etc.
- Popular modal libraries: React Modal, Chakra UI, Ant Design, Material-UI, etc.
- ARIA roles: `[role="dialog"]`, `[role="tooltip"]`, `[role="menu"]`, etc.

### Manual Portal Theming

```tsx
import {
  createThemedModal,
  applyThemeToElement,
  injectThemeIntoContainer
} from '@apollo/ui/theme'

// Create a themed modal container
const modalContainer = createThemedModal('my-modal-root')

// Apply theme to existing element
const existingPortal = document.getElementById('existing-portal')
applyThemeToElement(existingPortal, {
  '--apl-custom-variable': 'custom-value'
})

// Inject theme into any container
injectThemeIntoContainer(modalContainer, tokens)
```

## CSS Variable Access

All tokens are available as CSS variables with the `--apl-` prefix:

```css
.my-component {
  color: var(--apl-colors-content-primary-default);
  background: var(--apl-colors-surface-static-default1);
  padding: var(--apl-space-padding-md);
  border-radius: var(--apl-global-radius-md);
}

/* This works in modals and portals too! */
.modal-content {
  background: var(--apl-colors-surface-static-default1);
  border: 1px solid var(--apl-colors-border-default);
}
```

## Token Categories

### Global Tokens (Base Values)
- `--apl-global-color-*` - Base color palette
- `--apl-global-spacing-*` - Base spacing scale
- `--apl-global-font-*` - Base typography values
- `--apl-global-radius-*` - Base border radius values

### System Tokens (Semantic Values)
- `--apl-colors-content-*` - Text and content colors
- `--apl-colors-surface-*` - Background and surface colors
- `--apl-colors-border-*` - Border colors
- `--apl-space-*` - Semantic spacing values
- `--apl-typography-*` - Typography component styles

## Nested Themes

The NewThemeProvider supports nesting for complex theming scenarios:

```tsx
import { NewThemeProvider, useTheme, useThemeTokens } from '@apollo/ui/theme'

function App() {
  return (
    <NewThemeProvider mode="light" device="desktop">
      {/* Root theme */}
      <div>Root content with default Apollo theme</div>

      <NewThemeProvider
        customTokens={{ '--apl-colors-content-primary-default': '#e11d48' }}
        inheritParentTheme={true}
      >
        {/* Nested theme inherits parent settings but overrides primary color */}
        <div>Nested content with red primary color</div>

        <NewThemeProvider
          mode="dark"
          inheritParentTheme={false}
          themeId="independent-dark-theme"
        >
          {/* Independent theme with its own settings */}
          <div>Independent dark theme</div>
        </NewThemeProvider>
      </NewThemeProvider>
    </NewThemeProvider>
  )
}

// Access theme context in components
function ThemedComponent() {
  const theme = useTheme()
  const tokens = useThemeTokens()

  return (
    <div>
      Current theme depth: {theme?.depth}
      Available tokens: {Object.keys(tokens).length}
    </div>
  )
}
```

### Nested Theme Features:

- **Theme Inheritance**: Child themes inherit parent theme settings (mode, device, typography) when `inheritParentTheme={true}`
- **Token Composition**: Child themes merge parent tokens with their own custom tokens
- **Independent Themes**: Set `inheritParentTheme={false}` for completely independent nested themes
- **Automatic Scoping**: Each nested theme gets its own CSS scope automatically
- **Performance Optimization**: Only root theme injects global styles and portal CSS
- **Theme Context**: Access theme data via `useTheme()` and `useThemeTokens()` hooks

## W3C ThemeProvider

The W3C ThemeProvider loads tokens directly from the apollo-token W3C build files, providing automatic token resolution and reference handling:

```tsx
import { W3CThemeProvider, useW3CTheme, useW3CThemeTokens } from '@apollo/ui/theme'

function App() {
  return (
    <W3CThemeProvider mode="light" device="desktop" typography="baseline-16px">
      <YourApp />
    </W3CThemeProvider>
  )
}

// Access W3C theme context in components
function ThemedComponent() {
  const theme = useW3CTheme()
  const tokens = useW3CThemeTokens()

  return (
    <div style={{
      color: 'var(--apl-alias-color-on-surface-on-surface)',
      backgroundColor: 'var(--apl-alias-color-surface-surface)'
    }}>
      Current mode: {theme?.mode}
      Available tokens: {Object.keys(tokens).length}
    </div>
  )
}
```

### W3C ThemeProvider Features:

- **Direct W3C Token Loading**: Loads tokens from apollo-token build files automatically
- **Token Reference Resolution**: Automatically resolves `{token.reference}` syntax in W3C tokens
- **Multi-file Token Merging**: Combines global, base, and alias tokens from separate files
- **Mode-specific Tokens**: Loads light/dark mode tokens automatically
- **Device-responsive Tokens**: Loads mobile/handheld/desktop spacing tokens
- **Typography Baseline Support**: Supports 16px and 28px typography baselines
- **Nested Theme Support**: Full inheritance and composition like NewThemeProvider
- **Performance Optimized**: Efficient token loading and CSS injection
- **Portal Theming**: Automatic modal/dialog theming support

### Token File Structure:

The W3C ThemeProvider loads tokens from these apollo-token build files:
- `global/color.json` - Base color palette
- `system/base/color.json` - Semantic base colors
- `system/alias/color:light-mode.json` - Light mode color aliases
- `system/alias/color:dark-mode.json` - Dark mode color aliases
- `system/alias/spacing:desktop.json` - Desktop spacing tokens
- `system/alias/spacing:handheld.json` - Handheld spacing tokens
- `system/alias/spacing:mobile.json` - Mobile spacing tokens
- `system/alias/typography:baseline-16px.json` - 16px typography tokens
- `system/alias/typography:baseline-28px.json` - 28px typography tokens
