import React, { useState } from 'react'
import { createPortal } from 'react-dom'
import {
  NewThemeProvider,
  createThemedModal,
  applyThemeToElement
} from './NewThemeProvider'

// Example component that uses design tokens
const TokenDemo = () => {
  return (
    <div style={{
      padding: 'var(--apl-space-padding-lg)',
      backgroundColor: 'var(--apl-colors-surface-static-default1)',
      border: '1px solid var(--apl-colors-border-default)',
      borderRadius: 'var(--apl-global-radius-md)',
      marginBottom: 'var(--apl-space-gap-md)',
    }}>
      <h2 style={{
        color: 'var(--apl-colors-content-primary-default)',
        fontSize: 'var(--apl-font-font-size-head-line2)',
        fontWeight: 'var(--apl-font-font-weight-head-line2)',
        marginBottom: 'var(--apl-space-gap-sm)',
      }}>
        Design Token Demo
      </h2>
      
      <p style={{
        color: 'var(--apl-colors-content-default)',
        fontSize: 'var(--apl-font-font-size-body1)',
        lineHeight: 'var(--apl-font-line-height-body1)',
        marginBottom: 'var(--apl-space-gap-md)',
      }}>
        This component uses design tokens from the apollo-token build folder.
      </p>
      
      <div style={{
        display: 'flex',
        gap: 'var(--apl-space-gap-sm)',
        flexWrap: 'wrap',
      }}>
        <button style={{
          backgroundColor: 'var(--apl-colors-surface-action-primary-default)',
          color: 'var(--apl-colors-content-onaction)',
          border: 'none',
          padding: 'var(--apl-space-padding-sm) var(--apl-space-padding-md)',
          borderRadius: 'var(--apl-global-radius-sm)',
          fontSize: 'var(--apl-font-font-size-body2)',
          cursor: 'pointer',
        }}>
          Primary Button
        </button>
        
        <button style={{
          backgroundColor: 'var(--apl-colors-surface-action-secondary)',
          color: 'var(--apl-colors-content-default)',
          border: '1px solid var(--apl-colors-border-default)',
          padding: 'var(--apl-space-padding-sm) var(--apl-space-padding-md)',
          borderRadius: 'var(--apl-global-radius-sm)',
          fontSize: 'var(--apl-font-font-size-body2)',
          cursor: 'pointer',
        }}>
          Secondary Button
        </button>
        
        <button style={{
          backgroundColor: 'var(--apl-colors-surface-action-delete-default)',
          color: 'var(--apl-colors-content-onaction)',
          border: 'none',
          padding: 'var(--apl-space-padding-sm) var(--apl-space-padding-md)',
          borderRadius: 'var(--apl-global-radius-sm)',
          fontSize: 'var(--apl-font-font-size-body2)',
          cursor: 'pointer',
        }}>
          Danger Button
        </button>
      </div>
    </div>
  )
}

// Modal component that demonstrates portal theming
const ThemedModal = ({ isOpen, onClose, children }: {
  isOpen: boolean
  onClose: () => void
  children: React.ReactNode
}) => {
  const [modalContainer, setModalContainer] = useState<HTMLElement | null>(null)

  React.useEffect(() => {
    if (isOpen) {
      const container = createThemedModal('example-modal-root')

      // Demonstrate additional theme utilities
      applyThemeToElement(container, {
        '--apl-custom-modal-bg': 'rgba(0, 0, 0, 0.8)',
      })

      setModalContainer(container)
    }
    return () => {
      setModalContainer(null)
    }
  }, [isOpen])

  if (!isOpen || !modalContainer) return null

  return createPortal(
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1000,
    }}>
      <div style={{
        backgroundColor: 'var(--apl-colors-surface-static-default1)',
        border: '1px solid var(--apl-colors-border-default)',
        borderRadius: 'var(--apl-global-radius-lg)',
        padding: 'var(--apl-space-padding-xl)',
        maxWidth: '500px',
        width: '90%',
        maxHeight: '80vh',
        overflow: 'auto',
      }}>
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: 'var(--apl-space-gap-md)',
        }}>
          <h3 style={{
            color: 'var(--apl-colors-content-primary-default)',
            margin: 0,
            fontSize: 'var(--apl-font-font-size-head-line3)',
          }}>
            Themed Modal
          </h3>
          <button
            onClick={onClose}
            style={{
              background: 'none',
              border: 'none',
              fontSize: '24px',
              cursor: 'pointer',
              color: 'var(--apl-colors-content-default)',
              padding: '4px',
            }}
          >
            ×
          </button>
        </div>
        {children}
      </div>
    </div>,
    modalContainer
  )
}

// Example usage of the NewThemeProvider
export const ThemeProviderExample = () => {
  const [mode, setMode] = useState<'light' | 'dark'>('light')
  const [device, setDevice] = useState<'mobile' | 'handheld' | 'desktop'>('desktop')
  const [typography, setTypography] = useState<'16px' | '28px'>('16px')
  const [showModal, setShowModal] = useState(false)

  const customTokens = {
    '--apl-colors-content-primary-default': mode === 'dark' ? '#4ade80' : '#16a34a',
  }

  return (
    <div style={{ padding: '20px', fontFamily: 'system-ui, sans-serif' }}>
      <h1>Apollo Theme Provider Example</h1>
      <p>
        This example demonstrates the new ThemeProvider with support for dark mode, responsive spacing,
        typography variants, portal theming, and nested themes.
      </p>
      <p>
        <strong>See also:</strong> <code>NestedThemeExample.tsx</code> for advanced nested theme demonstrations.
      </p>
      
      {/* Controls */}
      <div style={{ 
        marginBottom: '20px', 
        padding: '16px', 
        backgroundColor: '#f5f5f5', 
        borderRadius: '8px',
        display: 'flex',
        gap: '16px',
        flexWrap: 'wrap',
        alignItems: 'center',
      }}>
        <label>
          Mode:
          <select
            value={mode}
            onChange={(e) => setMode(e.target.value as 'light' | 'dark')}
            style={{ marginLeft: '8px' }}
          >
            <option value="light">Light</option>
            <option value="dark">Dark</option>
          </select>
        </label>
        
        <label>
          Device:
          <select 
            value={device} 
            onChange={(e) => setDevice(e.target.value as any)}
            style={{ marginLeft: '8px' }}
          >
            <option value="mobile">Mobile</option>
            <option value="handheld">Handheld</option>
            <option value="desktop">Desktop</option>
          </select>
        </label>
        
        <label>
          Typography:
          <select 
            value={typography} 
            onChange={(e) => setTypography(e.target.value as any)}
            style={{ marginLeft: '8px' }}
          >
            <option value="16px">16px Baseline</option>
            <option value="28px">28px Baseline</option>
          </select>
        </label>
      </div>

      {/* Default Theme */}
      <NewThemeProvider
        mode={mode}
        device={device}
        typography={typography}
      >
        <TokenDemo />
      </NewThemeProvider>

      {/* Custom Theme with Scope */}
      <NewThemeProvider
        mode={mode}
        device={device}
        typography={typography}
        customTokens={customTokens}
        scope=".custom-theme"
        WrapperComponent="div"
        className="custom-theme"
      >
        <div style={{
          border: '2px dashed #ccc',
          borderRadius: '8px',
          padding: '16px',
        }}>
          <h3 style={{ marginTop: 0, color: '#666' }}>Custom Theme Scope</h3>
          <TokenDemo />

          <button
            onClick={() => setShowModal(true)}
            style={{
              backgroundColor: 'var(--apl-colors-surface-action-primary-default)',
              color: 'var(--apl-colors-content-onaction)',
              border: 'none',
              padding: 'var(--apl-space-padding-sm) var(--apl-space-padding-md)',
              borderRadius: 'var(--apl-global-radius-sm)',
              fontSize: 'var(--apl-font-font-size-body2)',
              cursor: 'pointer',
              marginTop: 'var(--apl-space-gap-md)',
            }}
          >
            Open Themed Modal
          </button>
        </div>
      </NewThemeProvider>

      {/* Modal Portal Demo */}
      <ThemedModal isOpen={showModal} onClose={() => setShowModal(false)}>
        <p style={{
          color: 'var(--apl-colors-content-default)',
          fontSize: 'var(--apl-font-font-size-body1)',
          lineHeight: 'var(--apl-font-line-height-body1)',
          marginBottom: 'var(--apl-space-gap-md)',
        }}>
          This modal is rendered in a portal but still has access to all the design tokens!
        </p>
        <TokenDemo />
        <p style={{
          color: 'var(--apl-colors-content-secondary)',
          fontSize: 'var(--apl-font-font-size-body2)',
          marginTop: 'var(--apl-space-gap-md)',
        }}>
          Notice how the modal inherits the theme tokens even though it's rendered outside the normal React tree.
        </p>
      </ThemedModal>

      {/* Token Reference */}
      <div style={{
        marginTop: '32px',
        padding: '16px',
        backgroundColor: '#f9f9f9',
        borderRadius: '8px',
      }}>
        <h3>Available Token Categories:</h3>
        <ul>
          <li><strong>Global Tokens:</strong> <code>--apl-global-*</code> (base values, always in :root)</li>
          <li><strong>Colors:</strong> <code>--apl-colors-*</code> (semantic colors)</li>
          <li><strong>Spacing:</strong> <code>--apl-space-*</code> (padding, gap, margin)</li>
          <li><strong>Typography:</strong> <code>--apl-font-*</code> (font properties)</li>
          <li><strong>Typography Styles:</strong> <code>--apl-typography-*</code> (complete text styles)</li>
        </ul>
        
        <h4>Current Configuration:</h4>
        <ul>
          <li>Mode: {mode}</li>
          <li>Device: {device}</li>
          <li>Typography Baseline: {typography}</li>
        </ul>
      </div>
    </div>
  )
}

export default ThemeProviderExample
