// Token loader for Apollo Design System
// Loads tokens from packages/apollo-token/build/tokens

// Global tokens (base tokens)
import globalColor from "../../../apollo-token/build/tokens/global/color.json"
import globalElevation from "../../../apollo-token/build/tokens/global/elevation.json"
import globalRadius from "../../../apollo-token/build/tokens/global/radius.json"
import globalSpacing from "../../../apollo-token/build/tokens/global/spacing.json"
import globalTypography from "../../../apollo-token/build/tokens/global/typography.json"

// System base tokens
import systemBaseColor from "../../../apollo-token/build/tokens/system/base/color.json"
import systemBaseElevation from "../../../apollo-token/build/tokens/system/base/elevation.json"
import systemBaseRadius from "../../../apollo-token/build/tokens/system/base/radius.json"
import systemBaseSpacing from "../../../apollo-token/build/tokens/system/base/spacing.json"
import systemBaseTypographyBaseline16 from "../../../apollo-token/build/tokens/system/base/typography:baseline-16px.json"
import systemBaseTypographyBaseline28 from "../../../apollo-token/build/tokens/system/base/typography:baseline-28px.json"

// System alias tokens (light mode as default)
import systemAliasColorLight from "../../../apollo-token/build/tokens/system/alias/color:light-mode.json"
import systemAliasColorDark from "../../../apollo-token/build/tokens/system/alias/color:dark-mode.json"
import systemAliasElevation from "../../../apollo-token/build/tokens/system/alias/elevation.json"
import systemAliasRadius from "../../../apollo-token/build/tokens/system/alias/radius.json"
import systemAliasSpacingDesktop from "../../../apollo-token/build/tokens/system/alias/spacing:desktop.json"
import systemAliasSpacingHandheld from "../../../apollo-token/build/tokens/system/alias/spacing:handheld.json"
import systemAliasSpacingMobile from "../../../apollo-token/build/tokens/system/alias/spacing:mobile.json"
import systemAliasTypographyBaseline16 from "../../../apollo-token/build/tokens/system/alias/typography:baseline-16px.json"
import systemAliasTypographyBaseline28 from "../../../apollo-token/build/tokens/system/alias/typography:baseline-28px.json"

export interface TokenCollection {
  [key: string]: any
}

export interface LoadedTokens {
  globalTokens: TokenCollection
  systemTokens: TokenCollection
  systemTokensDark?: TokenCollection
}

/**
 * Converts W3C token format to CSS variables
 */
function convertTokensToCSSVariables(tokens: any, prefix = "apl"): Record<string, string> {
  const cssVars: Record<string, string> = {}
  
  function traverse(obj: any, path: string[] = []) {
    for (const [key, value] of Object.entries(obj)) {
      if (key.startsWith('$')) {
        // Skip metadata properties
        continue
      }
      
      const currentPath = [...path, key]
      
      if (value && typeof value === 'object' && '$value' in value) {
        // This is a token with a value
        const varName = `--${prefix}-${currentPath.join('-')}`
        const tokenValue = value.$value

        if (typeof tokenValue === 'object' && tokenValue && 'hex' in tokenValue) {
          // Color token with hex value
          cssVars[varName] = String((tokenValue as { hex: string }).hex)
        } else if (typeof tokenValue === 'string' && tokenValue.startsWith('{') && tokenValue.endsWith('}')) {
          // Reference to another token
          const refPath = tokenValue.slice(1, -1).split('.')
          const refVarName = `--${prefix}-${refPath.join('-')}`
          cssVars[varName] = `var(${refVarName})`
        } else {
          // Direct value
          cssVars[varName] = String(tokenValue)
        }
      } else if (value && typeof value === 'object') {
        // Nested object, continue traversing
        traverse(value, currentPath)
      }
    }
  }
  
  traverse(tokens)
  return cssVars
}

/**
 * Loads and processes tokens from the build folder
 */
export function loadTokens(): LoadedTokens {
  // Merge global tokens
  const globalTokens = {
    ...globalColor,
    ...globalElevation,
    ...globalRadius,
    ...globalSpacing,
    ...globalTypography,
  }

  // Merge system base tokens
  const systemBaseTokens = {
    ...systemBaseColor,
    ...systemBaseElevation,
    ...systemBaseRadius,
    ...systemBaseSpacing,
    ...systemBaseTypographyBaseline16,
    ...systemBaseTypographyBaseline28,
  }

  // Merge system alias tokens (light mode)
  const systemAliasTokens = {
    ...systemAliasColorLight,
    ...systemAliasElevation,
    ...systemAliasRadius,
    ...systemAliasSpacingDesktop, // Default to desktop spacing
    ...systemAliasTypographyBaseline16, // Default to 16px baseline
  }

  // Merge system alias tokens (dark mode)
  const systemAliasTokensDark = {
    ...systemAliasColorDark,
    ...systemAliasElevation,
    ...systemAliasRadius,
    ...systemAliasSpacingDesktop,
    ...systemAliasTypographyBaseline16,
  }

  // Convert to CSS variables
  const globalCSSVars = convertTokensToCSSVariables(globalTokens)
  const systemBaseCSSVars = convertTokensToCSSVariables(systemBaseTokens)
  const systemAliasCSSVars = convertTokensToCSSVariables(systemAliasTokens)
  const systemAliasCSSVarsDark = convertTokensToCSSVariables(systemAliasTokensDark)

  return {
    globalTokens: globalCSSVars,
    systemTokens: {
      ...systemBaseCSSVars,
      ...systemAliasCSSVars,
    },
    systemTokensDark: {
      ...systemBaseCSSVars,
      ...systemAliasCSSVarsDark,
    },
  }
}

/**
 * Get responsive spacing tokens based on device type
 */
export function getResponsiveSpacing(device: 'mobile' | 'handheld' | 'desktop' = 'desktop') {
  const spacingMap = {
    mobile: systemAliasSpacingMobile,
    handheld: systemAliasSpacingHandheld,
    desktop: systemAliasSpacingDesktop,
  }
  
  return convertTokensToCSSVariables(spacingMap[device])
}

/**
 * Get typography tokens based on baseline
 */
export function getTypographyTokens(baseline: '16px' | '28px' = '16px') {
  const typographyMap = {
    '16px': {
      base: systemBaseTypographyBaseline16,
      alias: systemAliasTypographyBaseline16,
    },
    '28px': {
      base: systemBaseTypographyBaseline28,
      alias: systemAliasTypographyBaseline28,
    },
  }
  
  const selected = typographyMap[baseline]
  const merged = { ...selected.base, ...selected.alias }
  
  return convertTokensToCSSVariables(merged)
}
