import React, { useState } from 'react'
import { NewThemeProvider, useTheme, useThemeTokens } from './NewThemeProvider'

// Component that shows current theme info
const ThemeInfo = ({ label }: { label: string }) => {
  const theme = useTheme()
  const tokens = useThemeTokens()
  
  return (
    <div style={{
      padding: 'var(--apl-space-padding-sm)',
      backgroundColor: 'var(--apl-colors-surface-static-default2)',
      border: '1px solid var(--apl-colors-border-default)',
      borderRadius: 'var(--apl-global-radius-sm)',
      marginBottom: 'var(--apl-space-gap-sm)',
      fontSize: '12px',
      fontFamily: 'monospace',
    }}>
      <strong>{label}</strong><br />
      Depth: {theme?.depth ?? 'No theme'}<br />
      Mode: {theme?.mode}<br />
      Device: {theme?.device}<br />
      Typography: {theme?.typography}<br />
      Token Count: {Object.keys(tokens).length}
    </div>
  )
}

// Demo component that uses theme tokens
const ThemedCard = ({ title, children }: { title: string; children: React.ReactNode }) => {
  return (
    <div style={{
      backgroundColor: 'var(--apl-colors-surface-static-default1)',
      border: '1px solid var(--apl-colors-border-default)',
      borderRadius: 'var(--apl-global-radius-md)',
      padding: 'var(--apl-space-padding-lg)',
      marginBottom: 'var(--apl-space-gap-md)',
    }}>
      <h3 style={{
        color: 'var(--apl-colors-content-primary-default)',
        fontSize: 'var(--apl-font-font-size-head-line3)',
        marginTop: 0,
        marginBottom: 'var(--apl-space-gap-sm)',
      }}>
        {title}
      </h3>
      {children}
    </div>
  )
}

export const NestedThemeExample = () => {
  const [rootMode, setRootMode] = useState<'light' | 'dark'>('light')
  const [level1Mode, setLevel1Mode] = useState<'light' | 'dark'>('light')
  const [level2Mode, setLevel2Mode] = useState<'light' | 'dark'>('light')

  const level1CustomTokens = {
    '--apl-colors-content-primary-default': '#e11d48', // Red theme
    '--apl-colors-surface-action-primary-default': '#e11d48',
  }

  const level2CustomTokens = {
    '--apl-colors-content-primary-default': '#7c3aed', // Purple theme
    '--apl-colors-surface-action-primary-default': '#7c3aed',
    '--apl-global-radius-md': '16px', // More rounded
  }

  return (
    <div style={{ padding: '20px', fontFamily: 'system-ui, sans-serif' }}>
      <h1>Nested Theme Example</h1>
      
      {/* Root Theme Provider */}
      <NewThemeProvider mode={rootMode} device="desktop" typography="16px">
        <div style={{ border: '3px solid #3b82f6', padding: '16px', borderRadius: '8px' }}>
          <div style={{ marginBottom: '16px' }}>
            <h2 style={{ color: '#3b82f6', marginTop: 0 }}>Root Theme (Blue Border)</h2>
            <label>
              Mode:
              <select
                value={rootMode}
                onChange={(e) => setRootMode(e.target.value as 'light' | 'dark')}
                style={{ marginLeft: '8px' }}
              >
                <option value="light">Light</option>
                <option value="dark">Dark</option>
              </select>
            </label>
          </div>
          
          <ThemeInfo label="Root Theme Context" />
          
          <ThemedCard title="Root Level Card">
            <p style={{ 
              color: 'var(--apl-colors-content-default)',
              margin: 0,
            }}>
              This card uses the root theme tokens. Notice the default Apollo colors.
            </p>
          </ThemedCard>

          {/* Level 1 Nested Theme */}
          <NewThemeProvider
            mode={level1Mode}
            customTokens={level1CustomTokens}
            inheritParentTheme={true}
            themeId="level1-red-theme"
          >
            <div style={{ border: '3px solid #e11d48', padding: '16px', borderRadius: '8px' }}>
              <div style={{ marginBottom: '16px' }}>
                <h2 style={{ color: '#e11d48', marginTop: 0 }}>Level 1 Theme (Red Border)</h2>
                <label>
                  Mode:
                  <select
                    value={level1Mode}
                    onChange={(e) => setLevel1Mode(e.target.value as 'light' | 'dark')}
                    style={{ marginLeft: '8px' }}
                  >
                    <option value="light">Light (or inherit from root)</option>
                    <option value="dark">Dark</option>
                  </select>
                </label>
              </div>
              
              <ThemeInfo label="Level 1 Theme Context" />
              
              <ThemedCard title="Level 1 Card (Red Theme)">
                <p style={{ 
                  color: 'var(--apl-colors-content-default)',
                  marginBottom: 'var(--apl-space-gap-sm)',
                }}>
                  This card inherits from root but overrides primary colors to red.
                </p>
                <button style={{
                  backgroundColor: 'var(--apl-colors-surface-action-primary-default)',
                  color: 'var(--apl-colors-content-onaction)',
                  border: 'none',
                  padding: 'var(--apl-space-padding-sm) var(--apl-space-padding-md)',
                  borderRadius: 'var(--apl-global-radius-sm)',
                  cursor: 'pointer',
                }}>
                  Red Button
                </button>
              </ThemedCard>

              {/* Level 2 Nested Theme */}
              <NewThemeProvider
                mode={level2Mode}
                customTokens={level2CustomTokens}
                inheritParentTheme={true}
                themeId="level2-purple-theme"
              >
                <div style={{ border: '3px solid #7c3aed', padding: '16px', borderRadius: '8px' }}>
                  <div style={{ marginBottom: '16px' }}>
                    <h2 style={{ color: '#7c3aed', marginTop: 0 }}>Level 2 Theme (Purple Border)</h2>
                    <label>
                      Mode:
                      <select
                        value={level2Mode}
                        onChange={(e) => setLevel2Mode(e.target.value as 'light' | 'dark')}
                        style={{ marginLeft: '8px' }}
                      >
                        <option value="light">Light (or inherit from level 1)</option>
                        <option value="dark">Dark</option>
                      </select>
                    </label>
                  </div>
                  
                  <ThemeInfo label="Level 2 Theme Context" />
                  
                  <ThemedCard title="Level 2 Card (Purple Theme)">
                    <p style={{ 
                      color: 'var(--apl-colors-content-default)',
                      marginBottom: 'var(--apl-space-gap-sm)',
                    }}>
                      This card inherits from level 1 but overrides primary colors to purple and increases border radius.
                    </p>
                    <button style={{
                      backgroundColor: 'var(--apl-colors-surface-action-primary-default)',
                      color: 'var(--apl-colors-content-onaction)',
                      border: 'none',
                      padding: 'var(--apl-space-padding-sm) var(--apl-space-padding-md)',
                      borderRadius: 'var(--apl-global-radius-sm)',
                      cursor: 'pointer',
                    }}>
                      Purple Button (More Rounded)
                    </button>
                  </ThemedCard>

                  {/* Level 3 - Non-inheriting Theme */}
                  <NewThemeProvider
                    mode="dark"
                    device="mobile"
                    typography="28px"
                    inheritParentTheme={false}
                    customTokens={{
                      '--apl-colors-content-primary-default': '#059669', // Green
                      '--apl-colors-surface-action-primary-default': '#059669',
                    }}
                    themeId="level3-independent-theme"
                  >
                    <div style={{ border: '3px solid #059669', padding: '16px', borderRadius: '8px' }}>
                      <h2 style={{ color: '#059669', marginTop: 0 }}>Level 3 Independent Theme (Green Border)</h2>
                      <p style={{ fontSize: '14px', color: '#666', marginBottom: '16px' }}>
                        This theme does NOT inherit from parent. It uses its own dark mode, mobile device, and 28px typography.
                      </p>
                      
                      <ThemeInfo label="Level 3 Independent Theme Context" />
                      
                      <ThemedCard title="Level 3 Independent Card">
                        <p style={{ 
                          color: 'var(--apl-colors-content-default)',
                          fontSize: 'var(--apl-font-font-size-body1)',
                          marginBottom: 'var(--apl-space-gap-sm)',
                        }}>
                          This card uses completely independent theme settings with larger typography and mobile spacing.
                        </p>
                        <button style={{
                          backgroundColor: 'var(--apl-colors-surface-action-primary-default)',
                          color: 'var(--apl-colors-content-onaction)',
                          border: 'none',
                          padding: 'var(--apl-space-padding-sm) var(--apl-space-padding-md)',
                          borderRadius: 'var(--apl-global-radius-sm)',
                          cursor: 'pointer',
                        }}>
                          Green Independent Button
                        </button>
                      </ThemedCard>
                    </div>
                  </NewThemeProvider>
                </div>
              </NewThemeProvider>
            </div>
          </NewThemeProvider>
        </div>
      </NewThemeProvider>

      {/* Documentation */}
      <div style={{
        marginTop: '32px',
        padding: '16px',
        backgroundColor: '#f9f9f9',
        borderRadius: '8px',
      }}>
        <h3>Nested Theme Features:</h3>
        <ul>
          <li><strong>Theme Inheritance:</strong> Child themes can inherit parent theme settings (dark mode, device, typography)</li>
          <li><strong>Token Composition:</strong> Child themes merge parent tokens with their own custom tokens</li>
          <li><strong>Independent Themes:</strong> Set <code>inheritParentTheme={false}</code> for completely independent themes</li>
          <li><strong>Theme Context:</strong> Use <code>useTheme()</code> and <code>useThemeTokens()</code> hooks to access theme data</li>
          <li><strong>Automatic Scoping:</strong> Each nested theme gets its own CSS scope automatically</li>
          <li><strong>Performance:</strong> Only root theme injects global styles, nested themes only inject their specific overrides</li>
        </ul>
      </div>
    </div>
  )
}

export default NestedThemeExample
