'use client'

import { W3CThemeExample } from '@apollo/ui'

export default function W3CThemePage() {
  return (
    <div style={{ padding: '20px' }}>
      <h1>W3C Theme Provider Demo</h1>
      <p>
        This page demonstrates the W3C ThemeProvider component that loads tokens
        from the single w3c-tokens.json file with automatic filtering and reference resolution.
      </p>
      
      <div style={{ 
        border: '1px solid #e0e0e0', 
        borderRadius: '8px', 
        padding: '20px',
        backgroundColor: '#fafafa',
        marginTop: '20px'
      }}>
        <W3CThemeExample />
      </div>
      
      <div style={{ marginTop: '40px' }}>
        <h2>Features Demonstrated</h2>
        <ul>
          <li>Single file W3C token loading from w3c-tokens.json</li>
          <li>Style Dictionary-inspired token transforms and resolution</li>
          <li>Automatic token reference resolution ({`{base.spacing.space15}`} → 48px)</li>
          <li>Smart alias token filtering by mode, device, and typography</li>
          <li>Mode switching (light/dark)</li>
          <li>Device-responsive tokens (mobile/handheld/desktop)</li>
          <li>Typography baseline variants (16px/28px)</li>
          <li>Nested theme support with inheritance</li>
          <li>Custom token overrides</li>
          <li>Portal theming for modals</li>
        </ul>

        <div style={{ marginTop: '30px' }}>
          <h3>Token Processing Pipeline</h3>
          <p>Uses Style Dictionary-inspired transforms for robust token processing:</p>
          <ul>
            <li><code>/apollo-token/build/w3c-tokens.json</code> - Single comprehensive W3C token file</li>
            <li><strong>Smart Filtering:</strong> Extracts relevant alias tokens by mode/device/typography</li>
            <li><strong>Transform Pipeline:</strong> Applies W3C token transforms (color/w3c-hex, size/w3c-px, etc.)</li>
            <li><strong>Reference Resolution:</strong> Resolves {`{token.reference}`} chains automatically</li>
            <li><strong>CSS Generation:</strong> Converts to CSS variables with --apl- prefix</li>
            <li><strong>Browser Compatible:</strong> No build-time dependencies, works in runtime</li>
          </ul>
        </div>
      </div>
    </div>
  )
}
